# =============================================================================
# SCALARS - Custom scalar types used throughout the application
# =============================================================================
scalar uuid
scalar timestamptz
scalar jsonb
scalar citext
scalar bigint
scalar bytea
scalar numeric
scalar date

# Enum-like scalars for type safety
scalar desk_type
scalar compensation_frequency
scalar experience_level
scalar payment_terms
scalar ppp_fee_type
scalar sharing_type
scalar job_access_role
scalar job_type
scalar job_priority
scalar job_status
scalar work_arrangement

# =============================================================================
# COMPARISON EXPRESSIONS - For filtering and where clauses
# =============================================================================
input uuid_comparison_exp {
  _eq: uuid
  _neq: uuid
  _in: [uuid!]
  _nin: [uuid!]
  _is_null: Boolean
}

input String_comparison_exp {
  _eq: String
  _neq: String
  _in: [String!]
  _nin: [String!]
  _is_null: Boolean
  _like: String
  _ilike: String
  _similar: String
  _regex: String
  _iregex: String
}

input Boolean_comparison_exp {
  _eq: Boolean
  _neq: Boolean
  _is_null: Boolean
}

input Int_comparison_exp {
  _eq: Int
  _neq: Int
  _in: [Int!]
  _nin: [Int!]
  _is_null: Boolean
  _gt: Int
  _gte: Int
  _lt: Int
  _lte: Int
}

input timestamptz_comparison_exp {
  _eq: timestamptz
  _neq: timestamptz
  _in: [timestamptz!]
  _nin: [timestamptz!]
  _is_null: Boolean
  _gt: timestamptz
  _gte: timestamptz
  _lt: timestamptz
  _lte: timestamptz
}

# =============================================================================
# ORDER BY EXPRESSIONS - For sorting
# =============================================================================
enum order_by {
  asc
  asc_nulls_first
  asc_nulls_last
  desc
  desc_nulls_first
  desc_nulls_last
}

# =============================================================================
# CORE TYPES - Main entities in the system
# =============================================================================

# Users table (from nHost Auth)
type users {
  id: uuid!
  email: citext!
  displayName: String!
  defaultRole: String!
  isAnonymous: Boolean!
  createdAt: timestamptz!
  updatedAt: timestamptz!

  # Relationships
  user_profile: user_profiles
}

# User profiles table
type user_profiles {
  id: uuid!
  user_id: uuid!
  current_desk: desk_type!
  current_organization_id: uuid
  email_domain: String!
  company_name: String
  timezone: String
  onboarding_completed: Boolean!
  is_active: Boolean!
  created_at: timestamptz!
  updated_at: timestamptz!

  # Relationships
  user: users!
  organization: organizations
}

# Organizations table
type organizations {
  id: uuid!
  name: String!
  industry: String
  website: String
  created_at: timestamptz!
  updated_at: timestamptz!

  # Relationships
  members: [user_profiles!]!
  jobs: [jobs!]!
}

# Jobs table
type jobs {
  id: uuid!
  title: String!
  description: String
  status: job_status!
  priority: job_priority!
  job_type: job_type!
  experience_level: experience_level!
  work_arrangement: work_arrangement!

  # Location
  city: String
  state: String
  country: String

  # Compensation
  min_compensation: Int
  max_compensation: Int
  compensation_frequency: compensation_frequency!
  currency: String!

  # Relationships
  client_id: uuid!
  creator_id: uuid!
  organization_id: uuid

  created_at: timestamptz!
  updated_at: timestamptz!

  # Relationships
  client: organizations!
  creator: users!
  organization: organizations
}

# =============================================================================
# BOOLEAN EXPRESSIONS - For complex filtering
# =============================================================================
input users_bool_exp {
  _and: [users_bool_exp!]
  _not: users_bool_exp
  _or: [users_bool_exp!]
  id: uuid_comparison_exp
  email: String_comparison_exp
  displayName: String_comparison_exp
  defaultRole: String_comparison_exp
  isAnonymous: Boolean_comparison_exp
  createdAt: timestamptz_comparison_exp
  updatedAt: timestamptz_comparison_exp
  user_profile: user_profiles_bool_exp
}

input user_profiles_bool_exp {
  _and: [user_profiles_bool_exp!]
  _not: user_profiles_bool_exp
  _or: [user_profiles_bool_exp!]
  id: uuid_comparison_exp
  user_id: uuid_comparison_exp
  current_desk: String_comparison_exp
  current_organization_id: uuid_comparison_exp
  email_domain: String_comparison_exp
  company_name: String_comparison_exp
  timezone: String_comparison_exp
  onboarding_completed: Boolean_comparison_exp
  is_active: Boolean_comparison_exp
  created_at: timestamptz_comparison_exp
  updated_at: timestamptz_comparison_exp
  user: users_bool_exp
  organization: organizations_bool_exp
}

input organizations_bool_exp {
  _and: [organizations_bool_exp!]
  _not: organizations_bool_exp
  _or: [organizations_bool_exp!]
  id: uuid_comparison_exp
  name: String_comparison_exp
  industry: String_comparison_exp
  website: String_comparison_exp
  created_at: timestamptz_comparison_exp
  updated_at: timestamptz_comparison_exp
}

input jobs_bool_exp {
  _and: [jobs_bool_exp!]
  _not: jobs_bool_exp
  _or: [jobs_bool_exp!]
  id: uuid_comparison_exp
  title: String_comparison_exp
  description: String_comparison_exp
  status: String_comparison_exp
  priority: String_comparison_exp
  job_type: String_comparison_exp
  experience_level: String_comparison_exp
  work_arrangement: String_comparison_exp
  city: String_comparison_exp
  state: String_comparison_exp
  country: String_comparison_exp
  min_compensation: Int_comparison_exp
  max_compensation: Int_comparison_exp
  compensation_frequency: String_comparison_exp
  currency: String_comparison_exp
  client_id: uuid_comparison_exp
  creator_id: uuid_comparison_exp
  organization_id: uuid_comparison_exp
  created_at: timestamptz_comparison_exp
  updated_at: timestamptz_comparison_exp
  client: organizations_bool_exp
  creator: users_bool_exp
  organization: organizations_bool_exp
}

# =============================================================================
# ORDER BY TYPES - For sorting queries
# =============================================================================
input user_profiles_order_by {
  id: order_by
  user_id: order_by
  current_desk: order_by
  current_organization_id: order_by
  email_domain: order_by
  company_name: order_by
  timezone: order_by
  onboarding_completed: order_by
  is_active: order_by
  created_at: order_by
  updated_at: order_by
  user: users_order_by
}

input users_order_by {
  id: order_by
  email: order_by
  displayName: order_by
  defaultRole: order_by
  isAnonymous: order_by
  createdAt: order_by
  updatedAt: order_by
}

input jobs_order_by {
  id: order_by
  title: order_by
  status: order_by
  priority: order_by
  job_type: order_by
  experience_level: order_by
  work_arrangement: order_by
  city: order_by
  state: order_by
  country: order_by
  min_compensation: order_by
  max_compensation: order_by
  compensation_frequency: order_by
  currency: order_by
  created_at: order_by
  updated_at: order_by
}

input organizations_order_by {
  id: order_by
  name: order_by
  industry: order_by
  website: order_by
  created_at: order_by
  updated_at: order_by
}

# =============================================================================
# INSERT INPUT TYPES - For creating new records
# =============================================================================
input user_profiles_insert_input {
  id: uuid
  user_id: uuid!
  current_desk: desk_type!
  current_organization_id: uuid
  email_domain: String!
  company_name: String
  timezone: String
  onboarding_completed: Boolean
  is_active: Boolean
  created_at: timestamptz
  updated_at: timestamptz
}

input jobs_insert_input {
  id: uuid
  title: String!
  description: String
  status: job_status!
  priority: job_priority!
  job_type: job_type!
  experience_level: experience_level!
  work_arrangement: work_arrangement!
  city: String
  state: String
  country: String
  min_compensation: Int
  max_compensation: Int
  compensation_frequency: compensation_frequency!
  currency: String!
  client_id: uuid!
  creator_id: uuid!
  organization_id: uuid
  created_at: timestamptz
  updated_at: timestamptz
}

input organizations_insert_input {
  id: uuid
  name: String!
  industry: String
  website: String
  created_at: timestamptz
  updated_at: timestamptz
}

# =============================================================================
# SET INPUT TYPES - For updating existing records
# =============================================================================
input user_profiles_set_input {
  current_desk: desk_type
  current_organization_id: uuid
  company_name: String
  timezone: String
  onboarding_completed: Boolean
  is_active: Boolean
  updated_at: timestamptz
}

input jobs_set_input {
  title: String
  description: String
  status: job_status
  priority: job_priority
  job_type: job_type
  experience_level: experience_level
  work_arrangement: work_arrangement
  city: String
  state: String
  country: String
  min_compensation: Int
  max_compensation: Int
  compensation_frequency: compensation_frequency
  currency: String
  client_id: uuid
  creator_id: uuid
  organization_id: uuid
  updated_at: timestamptz
}

input organizations_set_input {
  name: String
  industry: String
  website: String
  updated_at: timestamptz
}

# =============================================================================
# MUTATION RESPONSE TYPES - For mutation results
# =============================================================================
type user_profiles_mutation_response {
  affected_rows: Int!
  returning: [user_profiles!]!
}

type jobs_mutation_response {
  affected_rows: Int!
  returning: [jobs!]!
}

type organizations_mutation_response {
  affected_rows: Int!
  returning: [organizations!]!
}

# =============================================================================
# ROOT TYPES - Query, Mutation, and Subscription entry points
# =============================================================================
type query_root {
  # User profiles
  user_profiles(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [user_profiles_order_by!]
    where: user_profiles_bool_exp
  ): [user_profiles!]!

  user_profiles_by_pk(id: uuid!): user_profiles

  # Users (from nHost Auth)
  users(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [users_order_by!]
    where: users_bool_exp
  ): [users!]!

  users_by_pk(id: uuid!): users

  # Jobs
  jobs(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [jobs_order_by!]
    where: jobs_bool_exp
  ): [jobs!]!

  jobs_by_pk(id: uuid!): jobs

  # Organizations
  organizations(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [organizations_order_by!]
    where: organizations_bool_exp
  ): [organizations!]!

  organizations_by_pk(id: uuid!): organizations
}

type mutation_root {
  # User profiles mutations
  insert_user_profiles_one(object: user_profiles_insert_input!): user_profiles
  insert_user_profiles(objects: [user_profiles_insert_input!]!): user_profiles_mutation_response

  update_user_profiles_by_pk(
    pk_columns: user_profiles_pk_columns_input!
    _set: user_profiles_set_input!
  ): user_profiles

  update_user_profiles(
    where: user_profiles_bool_exp!
    _set: user_profiles_set_input!
  ): user_profiles_mutation_response

  delete_user_profiles_by_pk(id: uuid!): user_profiles
  delete_user_profiles(where: user_profiles_bool_exp!): user_profiles_mutation_response

  # Jobs mutations
  insert_jobs_one(object: jobs_insert_input!): jobs
  insert_jobs(objects: [jobs_insert_input!]!): jobs_mutation_response

  update_jobs_by_pk(
    pk_columns: jobs_pk_columns_input!
    _set: jobs_set_input!
  ): jobs

  update_jobs(
    where: jobs_bool_exp!
    _set: jobs_set_input!
  ): jobs_mutation_response

  delete_jobs_by_pk(id: uuid!): jobs
  delete_jobs(where: jobs_bool_exp!): jobs_mutation_response

  # Organizations mutations
  insert_organizations_one(object: organizations_insert_input!): organizations
  insert_organizations(objects: [organizations_insert_input!]!): organizations_mutation_response

  update_organizations_by_pk(
    pk_columns: organizations_pk_columns_input!
    _set: organizations_set_input!
  ): organizations

  update_organizations(
    where: organizations_bool_exp!
    _set: organizations_set_input!
  ): organizations_mutation_response

  delete_organizations_by_pk(id: uuid!): organizations
  delete_organizations(where: organizations_bool_exp!): organizations_mutation_response
}

type subscription_root {
  # User profiles subscriptions
  user_profiles(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [user_profiles_order_by!]
    where: user_profiles_bool_exp
  ): [user_profiles!]!

  user_profiles_by_pk(id: uuid!): user_profiles

  # Jobs subscriptions
  jobs(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [jobs_order_by!]
    where: jobs_bool_exp
  ): [jobs!]!

  jobs_by_pk(id: uuid!): jobs

  # Organizations subscriptions
  organizations(
    distinct_on: [String!]
    limit: Int
    offset: Int
    order_by: [organizations_order_by!]
    where: organizations_bool_exp
  ): [organizations!]!

  organizations_by_pk(id: uuid!): organizations
}

# =============================================================================
# PK COLUMNS INPUT TYPES - For primary key operations
# =============================================================================
input user_profiles_pk_columns_input {
  id: uuid!
}

input jobs_pk_columns_input {
  id: uuid!
}

input organizations_pk_columns_input {
  id: uuid!
}

schema {
  query: query_root
  mutation: mutation_root
  subscription: subscription_root
}

"""whether this query should be cached (Hasura Cloud only)"""
directive @cached(
  """refresh the cache entry"""
  refresh: Boolean! = false

  """measured in seconds"""
  ttl: Int! = 60
) on QUERY

# Basic types
type user_profiles {
  id: uuid!
  user_id: uuid!
  current_desk: desk_type!
  current_organization_id: uuid
  email_domain: String!
  company_name: String
  timezone: String
  onboarding_completed: Boolean!
  is_active: Boolean!
  created_at: timestamptz!
  updated_at: timestamptz!
}

type user_profiles_mutation_response {
  affected_rows: Int!
  returning: [user_profiles!]!
}

type query_root {
  user_profiles(where: user_profiles_bool_exp): [user_profiles!]!
}

type mutation_root {
  update_user_profiles(where: user_profiles_bool_exp!, _set: user_profiles_set_input!): user_profiles_mutation_response
  insert_user_profiles_one(object: user_profiles_insert_input!): user_profiles
}
