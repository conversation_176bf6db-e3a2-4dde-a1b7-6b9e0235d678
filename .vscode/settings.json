{"files.exclude": {"**/.git": true, "**/.svn": true, "**/.hg": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/.vercel": true, ".vercel": true, "**/node_modules": true, "node_modules": true, "**/.gitignore": true, ".gitignore": true, "**/.prettierignore": true, ".prettierignore": true, "**/.prettierrc": true, ".prettierrc": true, "**/components.json": true, "*.json": true, "components.json": true, "**/*.json": true, "**/eslint.config.js": true, "*.js": true, "eslint.config.js": true, "**/*.js": true, "schema.graphql": true, "**/.vscode": true, ".vscode": true, ".svelte-kit": true, "**/.next": true, ".next": true, "**/.npmrc": true, ".npmrc": true, "**/.zencoder": true, ".zencoder": true, "**/.qodo": true, ".qodo": true}, "explorerExclude.backup": {}, "liveServer.settings.CustomBrowser": "chrome", "zencoder.enableRepoIndexing": true}