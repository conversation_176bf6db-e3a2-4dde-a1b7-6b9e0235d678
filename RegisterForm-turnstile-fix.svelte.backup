    try {
      // Pass Turnstile token to nHost if available
      const result = await authActions.signUp(email, password, {}, turnstileToken);
      
      if (result.error) {
        // Enhanced error handling for nHost specific errors
        let errorMessage = result.error.message || 'Registration failed';
        
        // Handle specific nHost error cases
        if (result.error.includes?.('turnstile') || 
            result.error.includes?.('cf-turnstile')) {
          errorMessage = 'Security verification failed. Please refresh the page and try again.';
        } else if (result.error.message?.toLowerCase().includes('pwned') || 
            result.error.message?.toLowerCase().includes('compromised') ||
            result.error.message?.toLowerCase().includes('breach')) {
          errorMessage = 'This password has been found in data breaches. Please choose a more unique password.';
        } else if (result.error.message?.toLowerCase().includes('password')) {
          errorMessage = 'Password does not meet security requirements. Use 8+ characters with uppercase, lowercase, number, and symbol.';
        } else if (result.error.message?.toLowerCase().includes('email')) {
          errorMessage = 'This email address cannot be used. Please try a different email.';
        } else if (result.error.message?.toLowerCase().includes('blocked')) {
          errorMessage = 'This email domain is not allowed for registration.';
        }
        
        console.error('SignUp error details:', result.error);
        toast.error(errorMessage);
        return;
      }

      toast.success('Registration successful! Please check your email to verify your account.');
      goto('/verify?email=' + encodeURIComponent(email));
      
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }