/**
 * Manual test script for email normalization
 * Run with: node test-email.mjs
 */

// Simple email normalization functions for testing
const GMAIL_DOMAINS = ['gmail.com', 'googlemail.com', 'google.com'];
const PLUS_ALIAS_DOMAINS = ['outlook.com', 'hotmail.com', 'live.com', 'fastmail.com', 'protonmail.com', 'yahoo.com'];

function isGmailDomain(domain) {
  return GMAIL_DOMAINS.includes(domain.toLowerCase());
}

function normalizeGmailAddress(email) {
  const [localPart, domain] = email.toLowerCase().split('@');
  
  if (!domain || !isGmailDomain(domain)) {
    return email.toLowerCase();
  }
  
  // Remove plus aliases (everything after +)
  const withoutPlus = localPart.split('+')[0];
  
  // Remove all dots from local part
  const withoutDots = withoutPlus.replace(/\./g, '');
  
  // Normalize domain to gmail.com
  return `${withoutDots}@gmail.com`;
}

function normalizeEmail(email) {
  if (!email || !email.includes('@')) {
    throw new Error('Invalid email address format');
  }
  
  const [localPart, domain] = email.toLowerCase().split('@');
  
  if (isGmailDomain(domain)) {
    return normalizeGmailAddress(email);
  } else if (PLUS_ALIAS_DOMAINS.includes(domain.toLowerCase())) {
    // Only remove plus aliases for non-Gmail providers
    const withoutPlus = localPart.split('+')[0];
    return `${withoutPlus}@${domain}`;
  } else {
    return email.toLowerCase();
  }
}

// Test cases
const testCases = [
  // Gmail normalization tests
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  
  // Other provider tests
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  
  // Real-world exploitation prevention
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' },
  { input: '<EMAIL>', expected: '<EMAIL>' }
];

console.log('🧪 Testing Email Normalization');
console.log('================================\n');

let passed = 0;
let failed = 0;

testCases.forEach((testCase, index) => {
  try {
    const result = normalizeEmail(testCase.input);
    const success = result === testCase.expected;
    
    if (success) {
      console.log(`✅ Test ${index + 1}: PASS`);
      console.log(`   Input:    ${testCase.input}`);
      console.log(`   Output:   ${result}`);
      console.log(`   Expected: ${testCase.expected}\n`);
      passed++;
    } else {
      console.log(`❌ Test ${index + 1}: FAIL`);
      console.log(`   Input:    ${testCase.input}`);
      console.log(`   Output:   ${result}`);
      console.log(`   Expected: ${testCase.expected}\n`);
      failed++;
    }
  } catch (error) {
    console.log(`💥 Test ${index + 1}: ERROR`);
    console.log(`   Input:    ${testCase.input}`);
    console.log(`   Error:    ${error.message}\n`);
    failed++;
  }
});

console.log('📊 Test Results');
console.log('================');
console.log(`✅ Passed: ${passed}`);
console.log(`❌ Failed: ${failed}`);
console.log(`📝 Total:  ${testCases.length}`);

if (failed === 0) {
  console.log('\n🎉 All tests passed! Email normalization is working correctly.');
} else {
  console.log(`\n⚠️  ${failed} test(s) failed. Please review the implementation.`);
}

// Test Gmail exploitation prevention
console.log('\n🔒 Gmail Exploitation Prevention Test');
console.log('=====================================');

const exploitEmails = [
  '<EMAIL>',
  '<EMAIL>', 
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>',
  '<EMAIL>'
];

const normalizedSet = new Set();
exploitEmails.forEach(email => {
  const normalized = normalizeEmail(email);
  normalizedSet.add(normalized);
  console.log(`${email} → ${normalized}`);
});

console.log(`\n📈 ${exploitEmails.length} potential exploit emails normalized to ${normalizedSet.size} unique address(es)`);
if (normalizedSet.size === 1) {
  console.log('✅ Gmail exploitation prevention is working correctly!');
} else {
  console.log('❌ Gmail exploitation prevention failed!');
}
