<script lang="ts">
  import { ArrowUpDown, ChevronDown } from "lucide-svelte/icons";
  import { createSvelteTable, FlexRender } from "$lib/components/ui/data-table";
  import { getCoreRowModel, getPaginationRowModel, getSortedRowModel, getFilteredRowModel } from "@tanstack/table-core";
  import type { ColumnDef, SortingState, ColumnFiltersState, VisibilityState } from "@tanstack/table-core";
  import { Button } from "$lib/components/ui/button";
  import { Input } from "$lib/components/ui/input";
  import * as Table from "$lib/components/ui/table";
  import * as DropdownMenu from "$lib/components/ui/dropdown-menu";
  import DataTableActions from "./data-table-actions.svelte";
  import type { Job } from "./job-types";
  
  let { data }: { data: Job[] } = $props();
  
  let sorting: SortingState = $state([]);
  let columnFilters: ColumnFiltersState = $state([]);
  let columnVisibility: VisibilityState = $state({});
  let globalFilter = $state("");
  
  // Column definitions with sorting and formatting
  const columns: ColumnDef<Job>[] = [
    {
      accessorKey: "title",
      header: ({ column }) => {
        return Button({
          variant: "ghost",
          onclick: () => column.toggleSorting(column.getIsSorted() === "asc"),
          children: ["Job Title", ArrowUpDown({ class: "ml-2 h-4 w-4" })]
        });
      },
      cell: ({ row }) => {
        const job = row.original;
        return `${job.title}`;
      }
    },
    {
      accessorKey: "company", 
      header: ({ column }) => {
        return Button({
          variant: "ghost",
          onclick: () => column.toggleSorting(column.getIsSorted() === "asc"),
          children: ["Company", ArrowUpDown({ class: "ml-2 h-4 w-4" })]
        });
      }
    },
    {
      accessorKey: "location",
      header: "Location"
    },
    {
      accessorKey: "salary",
      header: ({ column }) => {
        return Button({
          variant: "ghost", 
          onclick: () => column.toggleSorting(column.getIsSorted() === "asc"),
          children: ["Salary", ArrowUpDown({ class: "ml-2 h-4 w-4" })]
        });
      }
    },
    {
      accessorKey: "status",
      header: ({ column }) => {
        return Button({
          variant: "ghost",
          onclick: () => column.toggleSorting(column.getIsSorted() === "asc"), 
          children: ["Status", ArrowUpDown({ class: "ml-2 h-4 w-4" })]
        });
      },
      cell: ({ row }) => {
        const status = row.getValue("status") as string;
        const statusClass = 
          status === 'Open' ? 'bg-green-100 text-green-800' :
          status === 'Draft' ? 'bg-gray-100 text-gray-800' :
          status === 'Filled' ? 'bg-blue-100 text-blue-800' :
          'bg-red-100 text-red-800';
        
        return `<span class="px-2 py-1 text-xs font-medium rounded-full ${statusClass}">${status}</span>`;
      }
    },
    {
      accessorKey: "priority",
      header: "Priority",
      cell: ({ row }) => {
        const priority = row.getValue("priority") as string;
        const priorityClass =
          priority === 'A' ? 'bg-red-100 text-red-800' :
          priority === 'B' ? 'bg-orange-100 text-orange-800' :
          'bg-yellow-100 text-yellow-800';
          
        return `<span class="px-2 py-1 text-xs font-medium rounded-full ${priorityClass}">Priority ${priority}</span>`;
      }
    },
    {
      accessorKey: "posted",
      header: "Posted"
    },
    {
      id: "actions",
      cell: ({ row }) => {
        const job = row.original;
        return DataTableActions({ jobId: job.id });
      }
    }
  ];
  
  // Create table
  const table = createSvelteTable({
    get data() {
      return data;
    },
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      get sorting() {
        return sorting;
      },
      get columnFilters() {
        return columnFilters;
      },
      get columnVisibility() {
        return columnVisibility;
      },
      get globalFilter() {
        return globalFilter;
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater === 'function') {
        sorting = updater(sorting);
      } else {
        sorting = updater;
      }
    },
    onColumnFiltersChange: (updater) => {
      if (typeof updater === 'function') {
        columnFilters = updater(columnFilters);
      } else {
        columnFilters = updater;
      }
    },
    onColumnVisibilityChange: (updater) => {
      if (typeof updater === 'function') {
        columnVisibility = updater(columnVisibility);
      } else {
        columnVisibility = updater;
      }
    },
    onGlobalFilterChange: (updater) => {
      if (typeof updater === 'function') {
        globalFilter = updater(globalFilter);
      } else {
        globalFilter = updater;
      }
    }
  });
  
  // Column visibility options
  const visibleColumns = ["company", "location", "salary", "status", "priority", "posted"];
</script>

<div class="w-full">
  <!-- Controls -->
  <div class="flex items-center py-4 gap-4">
    <Input
      placeholder="Search jobs..."
      bind:value={globalFilter}
      class="max-w-sm"
    />
    
    <DropdownMenu.Root>
      <DropdownMenu.Trigger asChild let:builder>
        <Button variant="outline" class="ml-auto" builders={[builder]}>
          Columns <ChevronDown class="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenu.Trigger>
      <DropdownMenu.Content align="end">
        {#each visibleColumns as columnId}
          <DropdownMenu.CheckboxItem
            checked={columnVisibility[columnId] !== false}
            onCheckedChange={(value) => {
              columnVisibility = {
                ...columnVisibility,
                [columnId]: value
              };
            }}
          >
            {columnId.charAt(0).toUpperCase() + columnId.slice(1)}
          </DropdownMenu.CheckboxItem>
        {/each}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  </div>

  <!-- Table -->
  <div class="rounded-md border">
    <Table.Root>
      <Table.Header>
        {#each table.getHeaderGroups() as headerGroup}
          <Table.Row>
            {#each headerGroup.headers as header}
              <Table.Head>
                {#if !header.isPlaceholder}
                  <FlexRender content={header.column.columnDef.header} context={header.getContext()} />
                {/if}
              </Table.Head>
            {/each}
          </Table.Row>
        {/each}
      </Table.Header>
      <Table.Body>
        {#if table.getRowModel().rows?.length}
          {#each table.getRowModel().rows as row}
            <Table.Row data-state={row.getIsSelected() && "selected"}>
              {#each row.getVisibleCells() as cell}
                <Table.Cell>
                  <FlexRender content={cell.column.columnDef.cell} context={cell.getContext()} />
                </Table.Cell>
              {/each}
            </Table.Row>
          {/each}
        {:else}
          <Table.Row>
            <Table.Cell colspan={columns.length} class="h-24 text-center">
              No results.
            </Table.Cell>
          </Table.Row>
        {/if}
      </Table.Body>
    </Table.Root>
  </div>

  <!-- Pagination -->
  <div class="flex items-center justify-end space-x-2 py-4">
    <div class="text-muted-foreground flex-1 text-sm">
      {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
    </div>
    <div class="space-x-2">
      <Button
        variant="outline"
        size="sm"
        onclick={() => table.previousPage()}
        disabled={!table.getCanPreviousPage()}
      >
        Previous
      </Button>
      <Button
        variant="outline"
        size="sm"
        onclick={() => table.nextPage()}
        disabled={!table.getCanNextPage()}
      >
        Next
      </Button>
    </div>
  </div>
</div>
