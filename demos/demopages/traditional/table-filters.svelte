<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  // import { Badge } from '$lib/components/ui/badge';
  import { Label } from '$lib/components/ui/label';
  import * as Popover from '$lib/components/ui/popover';
  import { Calendar, X, Filter, RotateCcw } from 'lucide-svelte/icons';
  import type { Job } from './job-types';

  // Props - Svelte 5 style
  interface Props {
    jobs: Job[];
    onFiltersChange: (filters: FilterState) => void;
  }
  
  let { jobs = [], onFiltersChange }: Props = $props();

  // Filter state interface
  interface FilterState {
    statuses: string[];
    priorities: string[];
    locations: string[];
    experienceLevels: string[];
    dateRange: {
      from: string | null;
      to: string | null;
    };
  }

  // Current filter state
  let filters: FilterState = $state({
    statuses: [],
    priorities: [],
    locations: [],
    experienceLevels: [],
    dateRange: {
      from: null,
      to: null
    }
  });

  // Extract unique values from jobs data
  let availableStatuses = $derived([...new Set(jobs.map(job => job.status))].sort());
  let availablePriorities = $derived([...new Set(jobs.map(job => job.priority))].sort());
  let availableLocations = $derived([...new Set(jobs.map(job => job.location))].sort());
  let availableExperience = $derived([...new Set(jobs.map(job => job.experience))].sort());

  // Active filter count
  let activeFilterCount = $derived(
    filters.statuses.length + 
    filters.priorities.length + 
    filters.locations.length + 
    filters.experienceLevels.length +
    (filters.dateRange.from || filters.dateRange.to ? 1 : 0)
  );

  // Notify parent when filters change
  $effect(() => {
    onFiltersChange(filters);
  });

  // Multi-select handlers
  function toggleStatus(status: string) {
    if (filters.statuses.includes(status)) {
      filters.statuses = filters.statuses.filter(s => s !== status);
    } else {
      filters.statuses = [...filters.statuses, status];
    }
  }

  function togglePriority(priority: string) {
    if (filters.priorities.includes(priority)) {
      filters.priorities = filters.priorities.filter(p => p !== priority);
    } else {
      filters.priorities = [...filters.priorities, priority];
    }
  }

  function toggleLocation(location: string) {
    if (filters.locations.includes(location)) {
      filters.locations = filters.locations.filter(l => l !== location);
    } else {
      filters.locations = [...filters.locations, location];
    }
  }

  function toggleExperience(experience: string) {
    if (filters.experienceLevels.includes(experience)) {
      filters.experienceLevels = filters.experienceLevels.filter(e => e !== experience);
    } else {
      filters.experienceLevels = [...filters.experienceLevels, experience];
    }
  }

  // Clear all filters
  function clearAllFilters() {
    filters = {
      statuses: [],
      priorities: [],
      locations: [],
      experienceLevels: [],
      dateRange: {
        from: null,
        to: null
      }
    };
  }

  // Remove specific filter
  function removeFilter(type: string, value: string) {
    switch (type) {
      case 'status':
        filters.statuses = filters.statuses.filter(s => s !== value);
        break;
      case 'priority':
        filters.priorities = filters.priorities.filter(p => p !== value);
        break;
      case 'location':
        filters.locations = filters.locations.filter(l => l !== value);
        break;
      case 'experience':
        filters.experienceLevels = filters.experienceLevels.filter(e => e !== value);
        break;
      case 'dateRange':
        filters.dateRange = { from: null, to: null };
        break;
    }
  }
</script>

<div class="space-y-4">
  <!-- Filter Controls -->
  <div class="flex flex-wrap gap-3 items-center">
    <div class="flex items-center gap-2 text-sm font-medium">
      <Filter class="h-4 w-4" />
      Filters:
    </div>

    <!-- Status Filter -->
    <Popover.Root>
      <Popover.Trigger class="inline-flex h-8 items-center justify-center whitespace-nowrap rounded-md border border-input bg-background px-3 py-2 text-sm font-medium ring-offset-background transition-colors hover:bg-accent hover:text-accent-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50">
        Status
        {#if filters.statuses.length > 0}
          <span class="ml-2 h-5 px-1 text-xs bg-secondary text-secondary-foreground rounded">
            {filters.statuses.length}
          </span>
        {/if}
      </Popover.Trigger>
      <Popover.Content class="w-48 p-2">
        <div class="space-y-2">
          <div class="font-medium text-sm">Job Status</div>
          {#each availableStatuses as status}
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.statuses.includes(status)}
                onchange={() => toggleStatus(status)}
                class="rounded"
              />
              <span class="text-sm">{status}</span>
            </label>
          {/each}
        </div>
      </Popover.Content>
    </Popover.Root>

    <!-- Priority Filter -->
    <Popover.Root>
      <Popover.Trigger>
        <Button variant="outline" size="sm" class="h-8">
          Priority
          {#if filters.priorities.length > 0}
            <span class="ml-2 h-5 px-1 text-xs bg-secondary text-secondary-foreground rounded">
              {filters.priorities.length}
            </span>
          {/if}
        </Button>
      </Popover.Trigger>
      <Popover.Content class="w-48 p-2">
        <div class="space-y-2">
          <div class="font-medium text-sm">Priority Level</div>
          {#each availablePriorities as priority}
            <label class="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={filters.priorities.includes(priority)}
                onchange={() => togglePriority(priority)}
                class="rounded"
              />
              <span class="text-sm">Priority {priority}</span>
            </label>
          {/each}
        </div>
      </Popover.Content>
    </Popover.Root>

    <!-- Location Filter -->
    <Popover.Root>
      <Popover.Trigger>
        <Button variant="outline" size="sm" class="h-8">
          Location
          {#if filters.locations.length > 0}
            <span class="ml-2 h-5 px-1 text-xs bg-secondary text-secondary-foreground rounded">
              {filters.locations.length}
            </span>
          {/if}
        </Button>
      </Popover.Trigger>
      <Popover.Content class="w-56 p-2">
        <div class="space-y-2">
          <div class="font-medium text-sm">Location</div>
          <div class="max-h-40 overflow-y-auto space-y-2">
            {#each availableLocations as location}
              <label class="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.locations.includes(location)}
                  onchange={() => toggleLocation(location)}
                  class="rounded"
                />
                <span class="text-sm">{location}</span>
              </label>
            {/each}
          </div>
        </div>
      </Popover.Content>
    </Popover.Root>

    <!-- Experience Filter -->
    <Popover.Root>
      <Popover.Trigger>
        <Button variant="outline" size="sm" class="h-8">
          Experience
          {#if filters.experienceLevels.length > 0}
            <span class="ml-2 h-5 px-1 text-xs bg-secondary text-secondary-foreground rounded">
              {filters.experienceLevels.length}
            </span>
          {/if}
        </Button>
      </Popover.Trigger>
      <Popover.Content class="w-56 p-2">
        <div class="space-y-2">
          <div class="font-medium text-sm">Experience Level</div>
          <div class="max-h-40 overflow-y-auto space-y-2">
            {#each availableExperience as experience}
              <label class="flex items-center space-x-2 cursor-pointer">
                <input
                  type="checkbox"
                  checked={filters.experienceLevels.includes(experience)}
                  onchange={() => toggleExperience(experience)}
                  class="rounded"
                />
                <span class="text-sm">{experience}</span>
              </label>
            {/each}
          </div>
        </div>
      </Popover.Content>
    </Popover.Root>

    <!-- Date Range Filter -->
    <Popover.Root>
      <Popover.Trigger>
        <Button variant="outline" size="sm" class="h-8">
          <Calendar class="h-3 w-3 mr-1" />
          Posted Date
          {#if filters.dateRange.from || filters.dateRange.to}
            <span class="ml-2 h-5 px-1 text-xs bg-secondary text-secondary-foreground rounded">
              1
            </span>
          {/if}
        </Button>
      </Popover.Trigger>
      <Popover.Content class="w-64 p-3">
        <div class="space-y-3">
          <div class="font-medium text-sm">Posted Date Range</div>
          <div class="grid grid-cols-2 gap-2">
            <div>
              <Label class="text-xs text-muted-foreground">From</Label>
              <Input
                type="date"
                bind:value={filters.dateRange.from}
                class="h-8"
              />
            </div>
            <div>
              <Label class="text-xs text-muted-foreground">To</Label>
              <Input
                type="date"
                bind:value={filters.dateRange.to}
                class="h-8"
              />
            </div>
          </div>
        </div>
      </Popover.Content>
    </Popover.Root>

    <!-- Clear All Button -->
    {#if activeFilterCount > 0}
      <Button
        variant="ghost"
        size="sm"
        onclick={clearAllFilters}
        class="h-8 text-muted-foreground"
      >
        <RotateCcw class="h-3 w-3 mr-1" />
        Clear All ({activeFilterCount})
      </Button>
    {/if}
  </div>

  <!-- Active Filter Chips -->
  {#if activeFilterCount > 0}
    <div class="flex flex-wrap gap-2">
      <!-- Status chips -->
      {#each filters.statuses as status}
        <span class="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground gap-1">
          Status: {status}
          <button
            onclick={() => removeFilter('status', status)}
            class="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
          >
            <X class="h-2.5 w-2.5" />
          </button>
        </span>
      {/each}

      <!-- Priority chips -->
      {#each filters.priorities as priority}
        <span class="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground gap-1">
          Priority: {priority}
          <button
            onclick={() => removeFilter('priority', priority)}
            class="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
          >
            <X class="h-2.5 w-2.5" />
          </button>
        </span>
      {/each}

      <!-- Location chips -->
      {#each filters.locations as location}
        <span class="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground gap-1">
          {location}
          <button
            onclick={() => removeFilter('location', location)}
            class="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
          >
            <X class="h-2.5 w-2.5" />
          </button>
        </span>
      {/each}

      <!-- Experience chips -->
      {#each filters.experienceLevels as experience}
        <span class="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground gap-1">
          {experience}
          <button
            onclick={() => removeFilter('experience', experience)}
            class="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
          >
            <X class="h-2.5 w-2.5" />
          </button>
        </span>
      {/each}

      <!-- Date range chip -->
      {#if filters.dateRange.from || filters.dateRange.to}
        <span class="inline-flex items-center rounded-md bg-secondary px-2 py-1 text-xs font-medium text-secondary-foreground gap-1">
          {#if filters.dateRange.from && filters.dateRange.to}
            {filters.dateRange.from} - {filters.dateRange.to}
          {:else if filters.dateRange.from}
            From: {filters.dateRange.from}
          {:else if filters.dateRange.to}
            Until: {filters.dateRange.to}
          {/if}
          <button
            onclick={() => removeFilter('dateRange', '')}
            class="ml-1 hover:bg-secondary-foreground/20 rounded-full p-0.5"
          >
            <X class="h-2.5 w-2.5" />
          </button>
        </span>
      {/if}
    </div>
  {/if}
</div>
