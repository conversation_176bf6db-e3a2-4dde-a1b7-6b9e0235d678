<script lang="ts">
  import { Checkbox } from "$lib/components/ui/checkbox";
  
  interface Props {
    checked?: boolean;
    indeterminate?: boolean;
    onCheckedChange: (value: boolean) => void;
    ariaLabel?: string;
  }
  
  let { 
    checked = false, 
    indeterminate = false, 
    onCheckedChange, 
    ariaLabel = "" 
  }: Props = $props();

  function handleClick(event: MouseEvent) {
    // Stop event propagation to prevent row click from triggering
    event.stopPropagation();
  }

  function handleKeyDown(event: KeyboardEvent) {
    // Handle Enter and Space keys for accessibility
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      event.stopPropagation();
    }
  }
</script>

<div 
  class="checkbox-wrapper" 
  onclick={handleClick}
  onkeydown={handleKeyDown}
  role="presentation"
>
  <Checkbox 
    {checked} 
    {indeterminate}
    {onCheckedChange}
    aria-label={ariaLabel}
    class="translate-y-[2px]"
  />
</div>
