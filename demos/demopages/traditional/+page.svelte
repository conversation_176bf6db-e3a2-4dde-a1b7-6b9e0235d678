<script lang="ts">
  import { But<PERSON> } from '$lib/components/ui/button';
  import { Card, CardHeader, CardTitle, CardContent } from '$lib/components/ui/card';
  import { goto } from '$app/navigation';
  import type { Job } from './job-types';
  import DataTableSortableHeader from './data-table-sortable-header.svelte';
  import DataTableStatus from './data-table-status.svelte';
  import DataTableCheckbox from './data-table-checkbox.svelte';
  // import TableFilters from './table-filters.svelte';
  
  // TanStack Table imports
  import { createSvelteTable, FlexRender, renderComponent } from "$lib/components/ui/data-table";
  import { getCoreRowModel, getSortedRowModel, getFilteredRowModel, getPaginationRowModel } from "@tanstack/table-core";
  import type { ColumnDef, SortingState, ColumnFiltersState, VisibilityState, PaginationState, RowSelectionState } from "@tanstack/table-core";
  import { Input } from '$lib/components/ui/input';
  import * as Table from "$lib/components/ui/table";
  import * as DropdownMenu from "$lib/components/ui/dropdown-menu/index.js";
  import { ChevronDown, Trash2, Download, FileEdit } from "lucide-svelte/icons";
  import * as Select from "$lib/components/ui/select";

  // Mock jobs data for demo (expanded for pagination)
  const mockJobs: Job[] = [
    {
      id: '1',
      title: 'Senior Frontend Developer',
      company: 'TechCorp Inc',
      location: 'San Francisco, CA',
      salary: '$120k - $160k',
      status: 'Open',
      posted: '2 days ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'We are looking for a Senior Frontend Developer to join our growing team.'
    },
    {
      id: '2', 
      title: 'Product Manager',
      company: 'StartupXYZ',
      location: 'Remote',
      salary: '$90k - $130k',
      status: 'Open',
      posted: '1 week ago',
      priority: 'B',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Lead product strategy and roadmap development for our SaaS platform.'
    },
    {
      id: '3',
      title: 'Backend Engineer',
      company: 'CloudTech Solutions',
      location: 'Austin, TX',
      salary: '$100k - $140k',
      status: 'Open',
      posted: '3 days ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'Build scalable backend services and APIs.'
    },
    {
      id: '4',
      title: 'UX Designer',
      company: 'DesignLab',
      location: 'New York, NY',
      salary: '$80k - $110k',
      status: 'Draft',
      posted: '5 days ago',
      priority: 'B',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Create beautiful and intuitive user experiences.'
    },
    {
      id: '5',
      title: 'DevOps Engineer',
      company: 'InfraCorp',
      location: 'Seattle, WA',
      salary: '$110k - $150k',
      status: 'Open',
      posted: '1 day ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'Manage cloud infrastructure and deployment pipelines.'
    },
    {
      id: '6',
      title: 'Data Scientist',
      company: 'Analytics Pro',
      location: 'Boston, MA',
      salary: '$95k - $135k',
      status: 'Open',
      posted: '4 days ago',
      priority: 'B',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Analyze complex datasets and build predictive models.'
    },
    {
      id: '7',
      title: 'Mobile Developer',
      company: 'AppWorks',
      location: 'Los Angeles, CA',
      salary: '$85k - $120k',
      status: 'Open',
      posted: '6 days ago',
      priority: 'B',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Develop native iOS and Android applications.'
    },
    {
      id: '8',
      title: 'QA Engineer',
      company: 'QualityFirst',
      location: 'Remote',
      salary: '$70k - $95k',
      status: 'Open',
      posted: '1 week ago',
      priority: 'C',
      type: 'Full-time',
      experience: 'Junior (1-3 years)',
      description: 'Ensure software quality through comprehensive testing.'
    },
    {
      id: '9',
      title: 'Security Engineer',
      company: 'SecureNet',
      location: 'Washington, DC',
      salary: '$115k - $155k',
      status: 'Open',
      posted: '2 days ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'Protect systems and data from security threats.'
    },
    {
      id: '10',
      title: 'Technical Writer',
      company: 'DocuTech',
      location: 'Remote',
      salary: '$60k - $85k',
      status: 'Draft',
      posted: '1 week ago',
      priority: 'C',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Create clear technical documentation and user guides.'
    },
    {
      id: '11',
      title: 'AI/ML Engineer',
      company: 'FutureTech AI',
      location: 'San Francisco, CA',
      salary: '$130k - $180k',
      status: 'Open',
      posted: '3 days ago',
      priority: 'A',
      type: 'Full-time',
      experience: 'Senior (5+ years)',
      description: 'Develop and deploy machine learning models at scale.'
    },
    {
      id: '12',
      title: 'Sales Engineer',
      company: 'SalesTech',
      location: 'Chicago, IL',
      salary: '$85k - $120k',
      status: 'Open',
      posted: '5 days ago',
      priority: 'B',
      type: 'Full-time',
      experience: 'Mid-level (3-5 years)',
      description: 'Bridge technical solutions with customer needs.'
    }
  ];

  function viewJobDetails(jobId: string) {
    goto(`/demopages/traditional/${jobId}`);
  }

  // Handle row clicks - ignore if clicking on interactive elements
  function handleRowClick(event: MouseEvent, jobId: string) {
    const target = event.target as HTMLElement;
    
    // Don't navigate if clicking on interactive elements
    if (
      target.closest('button') || 
      target.closest('[role="checkbox"]') || 
      target.closest('input') ||
      target.closest('[data-slot="checkbox"]') || // Catch our shadcn checkbox
      target.closest('.checkbox-wrapper') // Catch our wrapper div
    ) {
      return;
    }
    
    viewJobDetails(jobId);
  }
  
  // TanStack Table state
  let pagination: PaginationState = $state({ pageIndex: 0, pageSize: 10 });
  let sorting: SortingState = $state([]);
  let columnFilters: ColumnFiltersState = $state([]);
  let columnVisibility: VisibilityState = $state({});
  let rowSelection: RowSelectionState = $state({});
  let globalFilter = $state("");
  let statusFilter = $state("");
  let priorityFilter = $state("");
  let locationFilter = $state("");
  
  // Enhanced columns with row selection
  const columns: ColumnDef<Job>[] = [
    {
      id: "select",
      header: ({ table }) =>
        renderComponent(DataTableCheckbox, {
          checked: table.getIsAllPageRowsSelected(),
          indeterminate: table.getIsSomePageRowsSelected() && !table.getIsAllPageRowsSelected(),
          onCheckedChange: (value) => table.toggleAllPageRowsSelected(!!value),
          ariaLabel: "Select all"
        }),
      cell: ({ row }) =>
        renderComponent(DataTableCheckbox, {
          checked: row.getIsSelected(),
          onCheckedChange: (value) => row.toggleSelected(!!value),
          ariaLabel: "Select row"
        }),
      enableSorting: false,
      enableHiding: false
    },
    {
      accessorKey: "title",
      header: ({ column }) => 
        renderComponent(DataTableSortableHeader, {
          onclick: () => column.toggleSorting(column.getIsSorted() === "asc"),
          children: "Job Title"
        }),
      enableSorting: true,
    },
    {
      accessorKey: "company", 
      header: ({ column }) => 
        renderComponent(DataTableSortableHeader, {
          onclick: () => column.toggleSorting(column.getIsSorted() === "asc"),
          children: "Company"
        }),
      enableSorting: true,
    },
    {
      accessorKey: "location",
      header: "Location", 
    },
    {
      accessorKey: "salary",
      header: "Salary",
    },
    {
      accessorKey: "status",
      header: "Status",
      cell: ({ row }) => 
        renderComponent(DataTableStatus, {
          status: row.getValue("status") as string
        })
    }
  ];

  // Filter state management
  interface FilterState {
    statuses: string[];
    priorities: string[];
    locations: string[];
    experienceLevels: string[];
    dateRange: {
      from: string | null;
      to: string | null;
    };
  }

  let currentFilters: FilterState = $state({
    statuses: [],
    priorities: [],
    locations: [],
    experienceLevels: [],
    dateRange: {
      from: null,
      to: null
    }
  });

  // Simple filtered data
  let filteredJobs = $derived(
    mockJobs.filter(job => {
      if (statusFilter && job.status !== statusFilter) return false;
      if (priorityFilter && job.priority !== priorityFilter) return false;
      if (locationFilter && job.location !== locationFilter) return false;
      return true;
    })
  );

  function clearAllFilters() {
    statusFilter = "";
    priorityFilter = "";
    locationFilter = "";
  }

  function handleFiltersChange(filters: FilterState) {
    currentFilters = filters;
  }
  
  // Create full-featured TanStack table
  const table = createSvelteTable({
    get data() {
      return filteredJobs; // Use filtered data
    },
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      get pagination() {
        return pagination;
      },
      get sorting() {
        return sorting;
      },
      get columnFilters() {
        return columnFilters;
      },
      get columnVisibility() {
        return columnVisibility;
      },
      get rowSelection() {
        return rowSelection;
      },
      get globalFilter() {
        return globalFilter;
      }
    },
    onPaginationChange: (updater) => {
      if (typeof updater === 'function') {
        pagination = updater(pagination);
      } else {
        pagination = updater;
      }
    },
    onSortingChange: (updater) => {
      if (typeof updater === 'function') {
        sorting = updater(sorting);
      } else {
        sorting = updater;
      }
    },
    onColumnFiltersChange: (updater) => {
      if (typeof updater === 'function') {
        columnFilters = updater(columnFilters);
      } else {
        columnFilters = updater;
      }
    },
    onColumnVisibilityChange: (updater) => {
      if (typeof updater === 'function') {
        columnVisibility = updater(columnVisibility);
      } else {
        columnVisibility = updater;
      }
    },
    onRowSelectionChange: (updater) => {
      if (typeof updater === 'function') {
        rowSelection = updater(rowSelection);
      } else {
        rowSelection = updater;
      }
    },
    onGlobalFilterChange: (updater) => {
      if (typeof updater === 'function') {
        globalFilter = updater(globalFilter);
      } else {
        globalFilter = updater;
      }
    }
  });

  // Bulk actions functionality
  let selectedRowCount = $derived(Object.keys(rowSelection).length);
  let selectedRows = $derived(table.getFilteredSelectedRowModel().rows);

  // Bulk action handlers
  function handleBulkDelete() {
    const selectedIds = selectedRows.map(row => row.original.id);
    console.log('Bulk delete:', selectedIds);
    // TODO: Implement actual delete logic
    // Reset selection after action
    rowSelection = {};
  }

  function handleBulkExport() {
    const selectedData = selectedRows.map(row => row.original);
    console.log('Bulk export:', selectedData);
    
    // Simple CSV export
    const headers = ['ID', 'Title', 'Company', 'Location', 'Salary', 'Status'];
    const csvContent = [
      headers.join(','),
      ...selectedData.map(job => [
        job.id,
        `"${job.title}"`,
        `"${job.company}"`,
        `"${job.location}"`,
        `"${job.salary}"`,
        job.status
      ].join(','))
    ].join('\n');
    
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `selected-jobs-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  }

  function handleBulkStatusUpdate(newStatus: 'Open' | 'Draft' | 'Filled' | 'Cancelled') {
    const selectedIds = selectedRows.map(row => row.original.id);
    console.log('Bulk status update:', selectedIds, 'to', newStatus);
    // TODO: Implement actual status update logic
    // Reset selection after action
    rowSelection = {};
  }

</script>

<svelte:head>
  <title>Traditional Pattern - Demo</title>
</svelte:head>

<div class="space-y-6">
  <!-- Demo Description -->
  <Card>
    <CardHeader>
      <CardTitle class="text-lg">Traditional Detail Page Pattern</CardTitle>
    </CardHeader>
    <CardContent>
      <p class="text-muted-foreground text-sm">
        **Step 4**: Complete table with advanced filtering system. Use filters above to narrow results, select rows for bulk actions.
      </p>
    </CardContent>
  </Card>

  <!-- Jobs Table - Full Featured TanStack -->
  <Card>
    <CardHeader>
      <CardTitle>Jobs List - Advanced Table with Filtering & Bulk Actions</CardTitle>
    </CardHeader>
    <CardContent>
      <!-- Simple Status Filter -->
      <div class="flex items-center gap-2 py-2">
        <select bind:value={statusFilter} class="border rounded px-2 py-1 text-sm">
          <option value="">All Statuses</option>
          <option value="Open">Open</option>
          <option value="Draft">Draft</option>
          <option value="Filled">Filled</option>
        </select>
        
        <select bind:value={priorityFilter} class="border rounded px-2 py-1 text-sm">
          <option value="">All Priorities</option>
          <option value="A">Priority A</option>
          <option value="B">Priority B</option>
          <option value="C">Priority C</option>
        </select>
        
        <select bind:value={locationFilter} class="border rounded px-2 py-1 text-sm">
          <option value="">All Locations</option>
          <option value="Remote">Remote</option>
          <option value="San Francisco, CA">San Francisco, CA</option>
          <option value="Austin, TX">Austin, TX</option>
          <option value="New York, NY">New York, NY</option>
          <option value="Seattle, WA">Seattle, WA</option>
          <option value="Boston, MA">Boston, MA</option>
          <option value="Los Angeles, CA">Los Angeles, CA</option>
          <option value="Washington, DC">Washington, DC</option>
          <option value="Chicago, IL">Chicago, IL</option>
        </select>
        
        <button 
          onclick={clearAllFilters}
          class="border rounded px-3 py-1 text-sm hover:bg-gray-50"
        >
          Clear All
        </button>
        
        <span class="text-sm text-gray-600 ml-4">
          Showing {filteredJobs.length} of {mockJobs.length} jobs
        </span>
      </div>
      
      <!-- Search Filter, Bulk Actions, and Column Toggle -->
      <div class="flex flex-col space-y-3 py-4 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
        <Input
          placeholder="Search jobs..."
          bind:value={globalFilter}
          class="flex-1 max-w-sm"
        />
        
        <div class="flex items-center space-x-2 sm:ml-auto">
          <!-- Bulk Actions Menu - Show when rows are selected -->
          {#if selectedRowCount > 0}
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                {#snippet child({ props })}
                  <Button {...props} variant="default" size="sm">
                    {selectedRowCount} selected
                    <ChevronDown class="ml-2 h-4 w-4" />
                  </Button>
                {/snippet}
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="start">
                <DropdownMenu.Label>Bulk Actions</DropdownMenu.Label>
                <DropdownMenu.Separator />
                
                <!-- Export Action -->
                <DropdownMenu.Item onclick={handleBulkExport}>
                  <Download class="mr-2 h-4 w-4" />
                  Export Selected
                </DropdownMenu.Item>
                
                <!-- Status Update Actions -->
                <DropdownMenu.Group>
                  <DropdownMenu.GroupHeading>Update Status</DropdownMenu.GroupHeading>
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate('Open')}>
                    <FileEdit class="mr-2 h-4 w-4" />
                    Set to Open
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate('Draft')}>
                    <FileEdit class="mr-2 h-4 w-4" />
                    Set to Draft
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate('Filled')}>
                    <FileEdit class="mr-2 h-4 w-4" />
                    Set to Filled
                  </DropdownMenu.Item>
                  <DropdownMenu.Item onclick={() => handleBulkStatusUpdate('Cancelled')}>
                    <FileEdit class="mr-2 h-4 w-4" />
                    Set to Cancelled
                  </DropdownMenu.Item>
                </DropdownMenu.Group>
                
                <DropdownMenu.Separator />
                
                <!-- Delete Action -->
                <DropdownMenu.Item onclick={handleBulkDelete} class="text-destructive">
                  <Trash2 class="mr-2 h-4 w-4" />
                  Delete Selected
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          {/if}
          
          <DropdownMenu.Root>
            <DropdownMenu.Trigger>
              {#snippet child({ props })}
                <Button {...props} variant="outline" size="sm">
                  Columns <ChevronDown class="ml-2 h-4 w-4" />
                </Button>
              {/snippet}
            </DropdownMenu.Trigger>
            <DropdownMenu.Content align="end">
              {#each table.getAllColumns().filter((col) => col.getCanHide()) as column (column.id)}
                <DropdownMenu.CheckboxItem
                  class="capitalize"
                  bind:checked={
                    () => column.getIsVisible(), 
                    (v) => column.toggleVisibility(!!v)
                  }
                >
                  {column.id}
                </DropdownMenu.CheckboxItem>
              {/each}
            </DropdownMenu.Content>
          </DropdownMenu.Root>
        </div>
      </div>
      
      <div class="rounded-md border overflow-x-auto">
        <Table.Root class="min-w-[800px]">
          <Table.Header>
            {#each table.getHeaderGroups() as headerGroup (headerGroup.id)}
              <Table.Row>
                {#each headerGroup.headers as header (header.id)}
                  <Table.Head>
                    {#if !header.isPlaceholder}
                      <FlexRender
                        content={header.column.columnDef.header}
                        context={header.getContext()}
                      />
                    {/if}
                  </Table.Head>
                {/each}
              </Table.Row>
            {/each}
          </Table.Header>
          <Table.Body>
            {#each table.getRowModel().rows as row (row.id)}
              <Table.Row class="cursor-pointer hover:bg-muted/50" onclick={(e) => handleRowClick(e, row.original.id)}>
                {#each row.getVisibleCells() as cell (cell.id)}
                  <Table.Cell>
                    <FlexRender
                      content={cell.column.columnDef.cell}
                      context={cell.getContext()}
                    />
                  </Table.Cell>
                {/each}
              </Table.Row>
            {:else}
              <Table.Row>
                <Table.Cell colspan={columns.length} class="h-24 text-center">
                  No results.
                </Table.Cell>
              </Table.Row>
            {/each}
          </Table.Body>
        </Table.Root>
      </div>
      
      <!-- Pagination Controls - Mobile Responsive -->
      <div class="flex flex-col space-y-4 py-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
        <!-- Row Count Display -->
        <div class="text-muted-foreground text-sm">
          {#if table.getFilteredSelectedRowModel().rows.length > 0}
            {table.getFilteredSelectedRowModel().rows.length} of {table.getFilteredRowModel().rows.length} row(s) selected.
          {:else if table.getFilteredRowModel().rows.length > 0}
            {@const startRow = table.getState().pagination.pageIndex * table.getState().pagination.pageSize + 1}
            {@const endRow = Math.min((table.getState().pagination.pageIndex + 1) * table.getState().pagination.pageSize, table.getFilteredRowModel().rows.length)}
            {@const totalRows = table.getFilteredRowModel().rows.length}
            Showing {startRow}-{endRow} of {totalRows} row(s)
          {:else}
            No results found
          {/if}
        </div>
        
        <!-- Pagination Controls -->
        <div class="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-x-6 sm:space-y-0 lg:space-x-8">
          <!-- Page Size Selector -->
          <div class="flex items-center space-x-2">
            <p class="text-sm font-medium whitespace-nowrap">Rows per page</p>
            <select 
              class="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
              bind:value={pagination.pageSize}
              onchange={() => {
                pagination = { ...pagination, pageIndex: 0 };
              }}
            >
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
              <option value={100}>100</option>
            </select>
          </div>
          
          <!-- Page Number and Navigation -->
          <div class="flex items-center justify-between space-x-4 sm:justify-center">
            <!-- Page Number Display -->
            <div class="text-sm font-medium whitespace-nowrap">
              Page {table.getState().pagination.pageIndex + 1} of {table.getPageCount()}
            </div>
            
            <!-- Navigation Buttons -->
            <div class="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onclick={() => table.previousPage()}
                disabled={!table.getCanPreviousPage()}
                class="whitespace-nowrap"
              >
                Previous
              </Button>
              <Button
                variant="outline"
                size="sm"
                onclick={() => table.nextPage()}
                disabled={!table.getCanNextPage()}
                class="whitespace-nowrap"
              >
                Next
              </Button>
            </div>
          </div>
        </div>
      </div>
    </CardContent>
  </Card>
</div>
