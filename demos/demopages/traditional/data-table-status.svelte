<script lang="ts">
  interface Props {
    status: string;
  }
  
  let { status }: Props = $props();
  
  let colorClass = $derived(
    status === 'Open' ? 'text-green-600 font-medium' :
    status === 'Draft' ? 'text-amber-600 font-medium' :
    status === 'Filled' ? 'text-red-600 font-medium' :
    status === 'On Hold' ? 'text-blue-600 font-medium' :
    'text-gray-500 font-medium'
  );
</script>

<span class={colorClass}>
  {status}
</span>
