<script lang="ts">
  import { MoreHorizontal } from "lucide-svelte/icons";
  import { Button } from "$lib/components/ui/button";
  import * as DropdownMenu from "$lib/components/ui/dropdown-menu";
  
  let { jobId }: { jobId: string } = $props();
  
  function handleAction(action: string) {
    alert(`${action} action for job: ${jobId}`);
  }
</script>

<DropdownMenu.Root>
  <DropdownMenu.Trigger asChild let:builder>
    <Button variant="ghost" builders={[builder]} size="icon" class="h-8 w-8 p-0">
      <span class="sr-only">Open menu</span>
      <MoreHorizontal class="h-4 w-4" />
    </Button>
  </DropdownMenu.Trigger>
  <DropdownMenu.Content align="end">
    <DropdownMenu.Label>Actions</DropdownMenu.Label>
    <DropdownMenu.Separator />
    <DropdownMenu.Item on:click={() => handleAction('View Details')}>
      View Details
    </DropdownMenu.Item>
    <DropdownMenu.Item on:click={() => handleAction('Edit')}>
      Edit Job
    </DropdownMenu.Item>
    <DropdownMenu.Item on:click={() => handleAction('Duplicate')}>
      Duplicate
    </DropdownMenu.Item>
    <DropdownMenu.Separator />
    <DropdownMenu.Item on:click={() => handleAction('Delete')} class="text-red-600">
      Delete
    </DropdownMenu.Item>
  </DropdownMenu.Content>
</DropdownMenu.Root>
