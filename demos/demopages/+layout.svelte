<script lang="ts">
  import { Button } from '$lib/components/ui/button';
  import { Card, CardHeader, CardTitle, CardContent } from '$lib/components/ui/card';
  import { page } from '$app/stores';
  
  // Demo navigation items
  const demoPages = [
    { path: '/demopages/traditional', label: 'Traditional Detail Page', description: 'Click → Navigate to detail page' },
    { path: '/demopages/preview', label: 'Quick Preview Popover', description: 'Hover/Click → Show popover preview' },
    { path: '/demopages/drawer', label: 'Drawer Pattern', description: 'Click → Open sliding drawer' }
  ];
  
  $: currentPath = $page.route.id;
</script>

<svelte:head>
  <title>Demo Pages - SourceFlex</title>
</svelte:head>

<div class="min-h-screen bg-background">
  <!-- Demo Header -->
  <header class="border-b bg-card">
    <div class="container mx-auto px-6 py-4">
      <h1 class="text-2xl font-bold text-foreground mb-2">Demo Pages</h1>
      <p class="text-muted-foreground text-sm">Testing different interaction patterns for SourceFlex</p>
    </div>
  </header>

  <!-- Demo Navigation -->
  <nav class="border-b bg-muted/30">
    <div class="container mx-auto px-6 py-3">
      <div class="flex gap-3 flex-wrap">
        {#each demoPages as demo}
          <Button 
            variant={currentPath === demo.path ? 'default' : 'outline'}
            size="sm"
            href={demo.path}
            class="text-xs"
          >
            {demo.label}
          </Button>
        {/each}
      </div>
    </div>
  </nav>

  <!-- Page Content -->
  <main class="container mx-auto px-6 py-6">
    <slot />
  </main>

  <!-- Demo Info Footer -->
  <footer class="mt-12 border-t bg-muted/20">
    <div class="container mx-auto px-6 py-4">
      <Card>
        <CardHeader>
          <CardTitle class="text-sm">Demo Pattern Comparison</CardTitle>
        </CardHeader>
        <CardContent>
          <div class="grid gap-3 md:grid-cols-3 text-xs">
            {#each demoPages as demo}
              <div class="space-y-1">
                <p class="font-medium">{demo.label}</p>
                <p class="text-muted-foreground">{demo.description}</p>
              </div>
            {/each}
          </div>
        </CardContent>
      </Card>
    </div>
  </footer>
</div>
