/**
 * Cloudflare Worker: Pre-Registration Email Validation
 * 
 * This function is called before nHost user registration to:
 * 1. Normalize email addresses (Gmail dot/plus alias prevention)
 * 2. Check for existing users with normalized email
 * 3. Return validation results to prevent duplicate accounts
 * 
 * Endpoint: POST /api/auth/validate-email
 */

import { normalizeEmail, validateEmailForRegistration } from '../src/lib/utils/email-normalization';

interface EmailValidationRequest {
  email: string;
  checkExisting?: boolean;
}

interface EmailValidationResponse {
  success: boolean;
  normalizedEmail: string;
  isAllowed: boolean;
  warnings: string[];
  errors: string[];
  emailChanged: boolean;
}

interface Environment {
  NHOST_SUBDOMAIN: string;
  NHOST_REGION: string; 
  NHOST_ADMIN_SECRET: string;
}

/**
 * Checks if normalized email already exists in nHost users table
 */
async function checkEmailExists(normalizedEmail: string, env: Environment): Promise<boolean> {
  const graphqlEndpoint = `https://${env.NHOST_SUBDOMAIN}.graphql.${env.NHOST_REGION}.nhost.run/v1`;
  
  const query = `
    query CheckEmailExists($email: String!) {
      authUsers(where: { email: { _eq: $email } }) {
        id
        email
      }
    }
  `;

  try {
    const response = await fetch(graphqlEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-hasura-admin-secret': env.NHOST_ADMIN_SECRET
      },
      body: JSON.stringify({
        query,
        variables: { email: normalizedEmail }
      })
    });

    const result = await response.json();
    
    if (result.errors) {
      console.error('GraphQL Error checking email:', result.errors);
      return false; // Allow registration if check fails
    }

    return result.data?.authUsers?.length > 0;
  } catch (error) {
    console.error('Error checking email existence:', error);
    return false; // Allow registration if check fails
  }
}

/**
 * Main handler for email validation
 */
export default {
  async fetch(request: Request, env: Environment): Promise<Response> {
    // Handle CORS preflight
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Max-Age': '86400'
        }
      });
    }

    // Only allow POST requests
    if (request.method !== 'POST') {
      return new Response('Method not allowed', { 
        status: 405,
        headers: {
          'Access-Control-Allow-Origin': '*'
        }
      });
    }

    try {
      const body: EmailValidationRequest = await request.json();
      
      if (!body.email) {
        return Response.json({
          success: false,
          errors: ['Email is required'],
          isAllowed: false,
          normalizedEmail: '',
          warnings: [],
          emailChanged: false
        } as EmailValidationResponse, {
          status: 400,
          headers: { 'Access-Control-Allow-Origin': '*' }
        });
      }

      // Validate and normalize email
      const validation = validateEmailForRegistration(body.email);
      const emailChanged = body.email.toLowerCase() !== validation.normalizedEmail;

      // Check if email already exists (if requested)
      let emailExists = false;
      if (body.checkExisting) {
        emailExists = await checkEmailExists(validation.normalizedEmail, env);
      }

      const response: EmailValidationResponse = {
        success: true,
        normalizedEmail: validation.normalizedEmail,
        isAllowed: !emailExists,
        warnings: validation.warnings,
        errors: emailExists ? ['An account with this email already exists'] : [],
        emailChanged
      };

      return Response.json(response, {
        headers: { 'Access-Control-Allow-Origin': '*' }
      });

    } catch (error) {
      console.error('Email validation error:', error);
      
      return Response.json({
        success: false,
        errors: [error instanceof Error ? error.message : 'Invalid email format'],
        isAllowed: false,
        normalizedEmail: '',
        warnings: [],
        emailChanged: false
      } as EmailValidationResponse, {
        status: 400,
        headers: { 'Access-Control-Allow-Origin': '*' }
      });
    }
  }
};
