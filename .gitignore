node_modules

# Output
.output
.vercel
.netlify
.wrangler
/.svelte-kit
/build

# <PERSON><PERSON><PERSON> generated files
$houdini
schema.graphql

# OS
.DS_Store
Thumbs.db

# Env
.env
.env.*
!.env.example
!.env.test
!.env.local.template

# Environment-specific overrides (security critical)
.env.local
.env.production
.env.staging
*.secret
*.key

# Vite
vite.config.js.timestamp-*
vite.config.ts.timestamp-*

# Documentation (stored in SF-knowledge repo only)
*.md
!README.md
