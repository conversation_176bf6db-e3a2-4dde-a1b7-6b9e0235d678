<!-- Example: Enhanced Login Page with Turnstile -->
<script lang="ts">
import { enhance } from '$app/forms';
import { goto } from '$app/navigation';
import TurnstileWrapper from '$lib/components/TurnstileWrapper.svelte';
import { authActions } from '$lib/stores/auth';
import toast from 'svelte-5-french-toast';

// Form state
let email = '';
let password = '';
let isLoading = false;
let turnstileToken = '';
let isVerified = false;

// Handle Turnstile verification
const handleTurnstileVerified = (event: CustomEvent) => {
  turnstileToken = event.detail.token;
  isVerified = true;
  toast.success('Security verification completed');
};

const handleTurnstileError = (event: CustomEvent) => {
  console.error('Turnstile error:', event.detail.error);
  toast.error('Security verification failed. Please try again.');
  isVerified = false;
};

// Form submission with Turnstile protection
const handleSubmit = enhance(({ formData }) => {
  if (!isVerified || !turnstileToken) {
    toast.error('Please complete security verification');
    return ({ update }) => update({ reset: false });
  }

  isLoading = true;
  formData.append('turnstile-token', turnstileToken);

  return async ({ result, update }) => {
    isLoading = false;

    if (result.type === 'success') {
      toast.success('Login successful!');
      goto('/dashboard');
    } else if (result.type === 'failure') {
      toast.error(result.data?.message || 'Login failed');
      // Reset Turnstile on failure
      isVerified = false;
      turnstileToken = '';
    }

    update();
  };
});
</script>

<svelte:head>
  <title>Sign In - SourceFlex</title>
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <!-- Header -->
    <div class="text-center">
      <h1 class="text-3xl font-bold text-blue-600 mb-2">SourceFlex</h1>
      <h2 class="text-xl font-semibold text-gray-900">Sign in to your account</h2>
      <p class="mt-2 text-sm text-gray-600">
        Or <a href="/auth/register" class="font-medium text-blue-600 hover:text-blue-500">
          create a new account
        </a>
      </p>
    </div>

    <!-- Login Form -->
    <div class="bg-white shadow-lg rounded-lg p-8">
      <form method="POST" action="?/login" use:handleSubmit class="space-y-6">
        <!-- Email Field -->
        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">
            Email address
          </label>
          <input
            id="email"
            name="email"
            type="email"
            required
            bind:value={email}
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your email"
          />
        </div>

        <!-- Password Field -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">
            Password
          </label>
          <input
            id="password"
            name="password"
            type="password"
            required
            bind:value={password}
            class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your password"
          />
        </div>

        <!-- Forgot Password Link -->
        <div class="flex items-center justify-between">
          <div class="text-sm">
            <a href="/auth/forgot-password" class="font-medium text-blue-600 hover:text-blue-500">
              Forgot your password?
            </a>
          </div>
        </div>

        <!-- Turnstile Security Verification -->
        <div class="border-t pt-6">
          <TurnstileWrapper
            title="Security Verification"
            description="Please verify you're human to sign in securely."
            on:verified={handleTurnstileVerified}
            on:error={handleTurnstileError}
          />
        </div>

        <!-- Submit Button -->
        <div>
          <button
            type="submit"
            disabled={isLoading || !isVerified}
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {#if isLoading}
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Signing in...
            {:else}
              Sign in
            {/if}
          </button>
        </div>
      </form>

      <!-- Alternative Login Methods -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300"></div>
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-white text-gray-500">Need help?</span>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-gray-600">
            Having trouble signing in? 
            <a href="/contact" class="font-medium text-blue-600 hover:text-blue-500">
              Contact support
            </a>
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
