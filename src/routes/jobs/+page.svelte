<!-- 
  Jobs List Page - Demonstrates correct Houdini usage
  Following the same pattern as admin/users/+page.svelte
-->

<script lang="ts">
  import { graphql } from '$houdini';
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';

  // ✅ CORRECT: Inline GraphQL query using <PERSON><PERSON><PERSON>
  // This will auto-generate types and reactive stores
  const jobsQuery = graphql(`
    query GetJobsList {
      jobs(order_by: { created_at: desc }, limit: 50) {
        id
        title
        status
        priority
        job_type
        experience_level
        city
        state
        country
        min_compensation
        max_compensation
        compensation_frequency
        currency
        created_at
        client {
          id
          name
          industry
        }
        creator {
          id
          displayName
          email
        }
      }
    }
  `);

  // ✅ This gives us reactive data with full type safety
  let { data: jobsData } = $derived(jobsQuery);
  let jobs = $derived(jobsData?.jobs || []);

  // Simple search functionality
  let searchTerm = $state('');
  let filteredJobs = $derived(
    jobs.filter(job => 
      job.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      job.client.name.toLowerCase().includes(searchTerm.toLowerCase())
    )
  );
</script>

<ProtectedRoute>
  {#snippet children()}
    <AppLayout>
      {#snippet children()}
  <div class="container mx-auto p-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-3xl font-bold">Jobs</h1>
      <button class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700">
        Create Job
      </button>
    </div>

    <!-- Search -->
    <div class="mb-4">
      <input
        type="text"
        placeholder="Search jobs..."
        bind:value={searchTerm}
        class="w-full max-w-md px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
      />
    </div>

    <!-- Jobs List -->
    <div class="grid gap-4">
      {#each filteredJobs as job (job.id)}
        <div class="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
          <!-- Job Header -->
          <div class="flex justify-between items-start mb-3">
            <div>
              <h3 class="text-xl font-semibold text-gray-900">{job.title}</h3>
              <p class="text-gray-600">{job.client.name}</p>
            </div>
            <div class="flex items-center gap-2">
              <!-- Status Badge -->
              <span class="px-2 py-1 text-xs font-medium rounded-full
                {job.status === 'open' ? 'bg-green-100 text-green-800' : 
                 job.status === 'draft' ? 'bg-gray-100 text-gray-800' :
                 job.status === 'filled' ? 'bg-blue-100 text-blue-800' :
                 'bg-red-100 text-red-800'}">
                {job.status}
              </span>
              <!-- Priority Badge -->
              <span class="px-2 py-1 text-xs font-medium rounded-full
                {job.priority === 'A' ? 'bg-red-100 text-red-800' :
                 job.priority === 'B' ? 'bg-orange-100 text-orange-800' :
                 job.priority === 'C' ? 'bg-yellow-100 text-yellow-800' :
                 'bg-gray-100 text-gray-800'}">
                Priority {job.priority}
              </span>
            </div>
          </div>

          <!-- Job Details -->
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
            <div>
              <span class="font-medium">Type:</span> {job.job_type}
            </div>
            <div>
              <span class="font-medium">Level:</span> {job.experience_level}
            </div>
            <div>
              <span class="font-medium">Location:</span> 
              {job.city ? `${job.city}, ${job.state}` : job.country}
            </div>
            <div>
              <span class="font-medium">Compensation:</span>
              {#if job.min_compensation && job.max_compensation}
                ${job.min_compensation.toLocaleString()} - ${job.max_compensation.toLocaleString()}
                {job.compensation_frequency ? `/${job.compensation_frequency}` : ''}
              {:else}
                Not specified
              {/if}
            </div>
          </div>

          <!-- Footer -->
          <div class="flex justify-between items-center mt-4 pt-4 border-t border-gray-100">
            <div class="text-sm text-gray-500">
              Created by {job.creator?.displayName || 'Unknown'} 
              on {new Date(job.created_at).toLocaleDateString()}
            </div>
            <div class="flex gap-2">
              <button class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                View
              </button>
              <button class="text-gray-600 hover:text-gray-800 text-sm font-medium">
                Edit
              </button>
            </div>
          </div>
        </div>
      {:else}
        <div class="text-center py-8 text-gray-500">
          {searchTerm ? 'No jobs found matching your search.' : 'No jobs found.'}
        </div>
      {/each}
    </div>

    <!-- Loading State -->
    {#if !jobsData}
      <div class="text-center py-8">
        <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <p class="mt-2 text-gray-600">Loading jobs...</p>
      </div>
    {/if}
  </div>
      {/snippet}
    </AppLayout>
  {/snippet}
</ProtectedRoute>
