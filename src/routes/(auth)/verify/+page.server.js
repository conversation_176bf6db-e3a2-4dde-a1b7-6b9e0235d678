import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    // Check for verification parameters from nHost
    const refreshToken = url.searchParams.get('refreshToken');
    const type = url.searchParams.get('type');
    const email = url.searchParams.get('email'); // Optional, may not be present
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for email verification
    return {
        authenticated: false,
        refreshToken: refreshToken,
        verificationToken: refreshToken, // Keep for backward compatibility
        type: type,
        email: email,
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
}
