import { redirect } from '@sveltejs/kit';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    // Get email from query params
    const email = url.searchParams.get('email');
    
    if (!email) {
        // If no email provided, redirect back to registration
        throw redirect(307, '/');
    }
    
    // Return data for the page
    return {
        authenticated: false,
        email: email
    };
}
