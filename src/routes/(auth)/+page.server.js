import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for unauthenticated users
    return {
        authenticated: false,
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
}
