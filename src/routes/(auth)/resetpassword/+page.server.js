import { redirect } from '@sveltejs/kit';
import { getEnvironmentConfig } from '$lib/utils/environment.js';

/** @type {import('./$types').PageServerLoad} */
export async function load({ locals, url }) {
    // If user is authenticated, redirect to dashboard
    if (locals.user) {
        throw redirect(307, '/dashboard');
    }
    
    // Check for reset token in URL params
    const token = url.searchParams.get('token');
    if (!token) {
        // No token provided, redirect to forgot password page
        throw redirect(307, '/forgotpassword');
    }
    
    const envConfig = getEnvironmentConfig();
    
    // Return data for password reset
    return {
        authenticated: false,
        resetToken: token,
        turnstileSiteKey: envConfig.turnstile.siteKey
    };
}
