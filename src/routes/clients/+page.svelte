<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import { Button } from '$lib/components/ui/button';
</script>

<svelte:head>
  <title>Clients - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  {#snippet children()}
    <AppLayout>
      {#snippet children()}
        <div class="p-6">
          <!-- Page Header -->
          <div class="mb-6">
            <h1 class="text-2xl font-bold text-foreground">Clients</h1>
            <p class="text-muted-foreground">Manage your client relationships and partnerships</p>
          </div>

          <!-- Action Bar -->
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
              <Button variant="default">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Client
              </Button>
              <Button variant="outline">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Import Contacts
              </Button>
              <Button variant="outline">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                </svg>
                Filter
              </Button>
            </div>
            <div class="flex items-center space-x-2">
              <Button variant="ghost" size="icon">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </Button>
              <Button variant="ghost" size="icon">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </Button>
            </div>
          </div>

          <!-- Stats Cards -->
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">Total Clients</p>
                  <p class="text-2xl font-bold">0</p>
                </div>
                <div class="h-8 w-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">Active</p>
                  <p class="text-2xl font-bold">0</p>
                </div>
                <div class="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">New This Month</p>
                  <p class="text-2xl font-bold">0</p>
                </div>
                <div class="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">Pending Follow-up</p>
                  <p class="text-2xl font-bold">0</p>
                </div>
                <div class="h-8 w-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Empty State -->
          <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col items-center justify-center py-12 px-6">
              <div class="h-12 w-12 bg-muted rounded-lg flex items-center justify-center mb-4">
                <svg class="h-6 w-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2">No clients found</h3>
              <p class="text-muted-foreground text-center mb-4">
                You haven't added any clients yet. Start building your client network.
              </p>
              <Button variant="default">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Your First Client
              </Button>
            </div>
          </div>
        </div>
      {/snippet}
    </AppLayout>
  {/snippet}
</ProtectedRoute>
