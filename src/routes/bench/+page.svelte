<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import { Button } from '$lib/components/ui/button';
</script>

<svelte:head>
  <title>Bench Sales - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  {#snippet children()}
    <AppLayout>
      {#snippet children()}
        <div class="p-6">
          <!-- Page Header -->
          <div class="mb-6">
            <h1 class="text-2xl font-bold text-foreground">Bench Sales</h1>
            <p class="text-muted-foreground">Manage available consultants and bench sales opportunities</p>
          </div>

          <!-- Action Bar -->
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center space-x-4">
              <Button variant="default">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Consultant
              </Button>
              <Button variant="outline">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Market Rates
              </Button>
              <Button variant="outline">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.207A1 1 0 013 6.5V4z" />
                </svg>
                Filter
              </Button>
            </div>
            <div class="flex items-center space-x-2">
              <Button variant="ghost" size="icon">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </Button>
              <Button variant="ghost" size="icon">
                <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16" />
                </svg>
              </Button>
            </div>
          </div>

          <!-- Stats Cards -->
          <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4 mb-6">
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">Available Consultants</p>
                  <p class="text-2xl font-bold">0</p>
                </div>
                <div class="h-8 w-8 bg-primary/10 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">On Projects</p>
                  <p class="text-2xl font-bold">0</p>
                </div>
                <div class="h-8 w-8 bg-blue-100 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">Potential Revenue</p>
                  <p class="text-2xl font-bold">$0</p>
                </div>
                <div class="h-8 w-8 bg-green-100 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
              </div>
            </div>
            
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <div class="flex items-center">
                <div class="flex-1">
                  <p class="text-sm font-medium text-muted-foreground">Avg. Rate</p>
                  <p class="text-2xl font-bold">$0/hr</p>
                </div>
                <div class="h-8 w-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <svg class="h-4 w-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
              </div>
            </div>
          </div>

          <!-- Technology Filter Tabs -->
          <div class="mb-6">
            <div class="border-b border-border">
              <nav class="-mb-px flex space-x-8">
                <button class="border-b-2 border-primary py-2 px-1 text-sm font-medium text-primary">
                  All Technologies
                </button>
                <button class="border-transparent py-2 px-1 text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                  Java
                </button>
                <button class="border-transparent py-2 px-1 text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                  .NET
                </button>
                <button class="border-transparent py-2 px-1 text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                  React
                </button>
                <button class="border-transparent py-2 px-1 text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                  Python
                </button>
                <button class="border-transparent py-2 px-1 text-sm font-medium text-muted-foreground hover:text-foreground hover:border-border">
                  AWS
                </button>
              </nav>
            </div>
          </div>

          <!-- Empty State -->
          <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
            <div class="flex flex-col items-center justify-center py-12 px-6">
              <div class="h-12 w-12 bg-muted rounded-lg flex items-center justify-center mb-4">
                <svg class="h-6 w-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 2l3.09 6.26L22 9l-5 4.87L18.18 22 12 18.27 5.82 22 7 13.87 2 9l6.91-.74L12 2z" />
                </svg>
              </div>
              <h3 class="text-lg font-semibold mb-2">No consultants available</h3>
              <p class="text-muted-foreground text-center mb-4">
                You haven't added any consultants to your bench yet. Start by adding available consultants.
              </p>
              <Button variant="default">
                <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add Your First Consultant
              </Button>
            </div>
          </div>
        </div>
      {/snippet}
    </AppLayout>
  {/snippet}
</ProtectedRoute>
