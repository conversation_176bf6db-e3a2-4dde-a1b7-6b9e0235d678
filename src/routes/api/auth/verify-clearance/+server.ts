import { json, error } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { verifyTurnstileToken, setClearanceSession } from '$lib/middleware/turnstile';

/**
 * API endpoint to verify Turnstile pre-clearance token
 * 
 * This endpoint validates the Turnstile token and sets up the session
 * for seamless access to auth pages.
 */
export const POST: RequestHandler = async ({ request, getClientAddress, ...event }) => {
  try {
    const { token } = await request.json();

    if (!token) {
      throw error(400, 'Turnstile token is required');
    }

    // Verify token with Cloudflare
    const clientIP = getClientAddress();
    const verificationResult = await verifyTurnstileToken(token, clientIP);

    if (!verificationResult.success) {
      console.error('Turnstile verification failed:', verificationResult['error-codes']);
      throw error(403, 'Security verification failed');
    }

    // Set clearance session
    setClearanceSession(event);

    return json({ 
      success: true, 
      message: 'Verification successful',
      timestamp: new Date().toISOString()
    });

  } catch (err) {
    console.error('Verification endpoint error:', err);
    
    if (err instanceof Error && 'status' in err) {
      throw err; // Re-throw SvelteKit errors
    }
    
    throw error(500, 'Internal server error');
  }
};
