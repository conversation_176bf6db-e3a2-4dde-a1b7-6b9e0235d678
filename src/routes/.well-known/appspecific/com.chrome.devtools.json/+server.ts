import { json, type RequestHandler } from '@sveltejs/kit';

// Specific handler for Chrome DevTools configuration
export const GET: RequestHandler = async () => {
	return json({
		version: 1,
		// Empty configuration object - tells Chrome DevTools no special debugging config needed
		targets: []
	}, {
		headers: {
			'Content-Type': 'application/json',
			'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
			'Access-Control-Allow-Origin': '*' // Allow DevTools access
		}
	});
};
