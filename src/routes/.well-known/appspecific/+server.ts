import { json, type RequestHandler } from '@sveltejs/kit';

// Handle Chrome DevTools app-specific configuration requests
export const GET: RequestHandler = async ({ url }) => {
	const pathname = url.pathname;
	
	// Handle specific Chrome DevTools request
	if (pathname.includes('com.chrome.devtools.json')) {
		return json({
			version: 1,
			// Empty configuration - tells Chrome DevTools no special config needed
		}, {
			headers: {
				'Content-Type': 'application/json',
				'Cache-Control': 'public, max-age=86400' // Cache for 24 hours
			}
		});
	}
	
	// For other .well-known requests, return 404
	return new Response('Not Found', { status: 404 });
};
