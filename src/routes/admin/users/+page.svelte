<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import { graphql } from '$houdini';
  import type { AdminUser } from '$lib/types/auth.js';

  // Houdini GraphQL store - Real query with user relationship
  const allUsersQuery = graphql(`
    query GetAllUsers {
      user_profiles {
        id
        user_id
        current_desk
        current_organization_id
        email_domain
        company_name
        timezone
        onboarding_completed
        is_active
        created_at
        updated_at
        user {
          id
          email
          displayName
          defaultRole
        }
      }
    }
  `);
  let searchTerm = $state('');
  let selectedRole = $state('all');

  // Real users data from GraphQL
  let allUsers = $derived(
    $allUsersQuery.data?.user_profiles || []
  );

  // Computed filtered users
  let filteredUsers = $derived(allUsers.filter(user => {
    const matchesSearch = searchTerm === '' || 
      user.user?.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.user?.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.company_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesRole = selectedRole === 'all' || user.user?.defaultRole === selectedRole;

    return matchesSearch && matchesRole;
  }));

  // Pagination state
  let currentPage = $state(1);
  let itemsPerPage = 10;
  let totalPages = $derived(Math.ceil(filteredUsers.length / itemsPerPage));
  let paginatedUsers = $derived(
    filteredUsers.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)
  );

  function nextPage() {
    if (currentPage < totalPages) currentPage++;
  }

  function prevPage() {
    if (currentPage > 1) currentPage--;
  }

  function goToPage(page: number) {
    currentPage = page;
  }
</script>

<svelte:head>
  <title>Admin - User Management - SourceFlex</title>
</svelte:head>

<ProtectedRoute allowedRoles={['sf_admin']}>
  {#snippet children()}
    <div class="min-h-screen bg-gray-50">
      <!-- Navigation Header -->
      <nav class="bg-white shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div class="flex justify-between h-16">
            <div class="flex items-center">
              <h1 class="text-xl font-semibold text-gray-900">Admin Dashboard</h1>
            </div>
            <div class="flex items-center space-x-4">
              <a href="/dashboard" class="text-blue-600 hover:text-blue-800">
                ← Back to Dashboard
              </a>
            </div>
          </div>
        </div>
      </nav>

      <!-- Main Content -->
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="md:flex md:items-center md:justify-between mb-8">
          <div>
            <h2 class="text-3xl font-bold text-gray-900">User Management</h2>
            <p class="mt-2 text-gray-600">
              Manage user accounts, roles, and permissions
            </p>
          </div>
          <div class="mt-4 md:mt-0">
            <button type="button" 
              class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              Add New User
            </button>
          </div>
        </div>

        <!-- Filters -->
        <div class="bg-white shadow rounded-lg mb-6">
          <div class="px-6 py-4 border-b border-gray-200">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <!-- Search -->
              <div>
                <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
                  Search Users
                </label>
                <input
                  type="text"
                  id="search"
                  bind:value={searchTerm}
                  placeholder="Search by email, name, or company..."
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <!-- Role Filter -->
              <div>
                <label for="role" class="block text-sm font-medium text-gray-700 mb-1">
                  Filter by Role
                </label>
                <select
                  id="role"
                  bind:value={selectedRole}
                  class="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Roles</option>
                  <option value="sf_admin">Super Admin</option>
                  <option value="org_manager">Organization Manager</option>
                  <option value="user">User</option>
                </select>
              </div>

              <!-- Results Count -->
              <div class="flex items-end">
                <div class="text-sm text-gray-600">
                  Showing {filteredUsers.length} user{filteredUsers.length !== 1 ? 's' : ''}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Users Table -->
        <div class="bg-white shadow rounded-lg overflow-hidden">
          <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    User
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Company
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Role
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                {#each paginatedUsers as user}
                  <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center">
                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <span class="text-blue-600 font-medium text-sm">
                            {user.user?.displayName?.charAt(0) || user.user?.email?.charAt(0) || '?'}
                          </span>
                        </div>
                        <div class="ml-4">
                          <div class="text-sm font-medium text-gray-900">
                            {user.user?.displayName || 'No name'}
                          </div>
                          <div class="text-sm text-gray-500">
                            {user.user?.email || 'No email'}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="text-sm text-gray-900">{user.company_name || 'No company'}</div>
                      <div class="text-sm text-gray-500">{user.email_domain}</div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                        {user.user?.defaultRole === 'sf_admin' ? 'bg-purple-100 text-purple-800' :
                         user.user?.defaultRole === 'org_manager' ? 'bg-blue-100 text-blue-800' :
                         'bg-gray-100 text-gray-800'}">
                        {user.user?.defaultRole || 'user'}
                      </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex flex-col space-y-1">
                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                          {user.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">
                          {user.is_active ? 'Active' : 'Inactive'}
                        </span>
                        {#if !user.onboarding_completed}
                          <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                            Pending Setup
                          </span>
                        {/if}
                      </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div class="flex space-x-3">
                        <button class="text-blue-600 hover:text-blue-900">View</button>
                        <button class="text-green-600 hover:text-green-900">Edit</button>
                        <button class="text-red-600 hover:text-red-900">Disable</button>
                      </div>
                    </td>
                  </tr>
                {/each}

                {#if paginatedUsers.length === 0}
                  <tr>
                    <td colspan="5" class="px-6 py-12 text-center">
                      <div class="text-gray-500">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No users found</h3>
                        <p class="mt-1 text-sm text-gray-500">Try adjusting your search or filter criteria.</p>
                      </div>
                    </td>
                  </tr>
                {/if}
              </tbody>
            </table>
          </div>

          <!-- Pagination -->
          {#if totalPages > 1}
            <div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
              <div class="flex-1 flex justify-between sm:hidden">
                <button
                  onclick={prevPage}
                  disabled={currentPage === 1}
                  class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Previous
                </button>
                <button
                  onclick={nextPage}
                  disabled={currentPage === totalPages}
                  class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Next
                </button>
              </div>
              <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p class="text-sm text-gray-700">
                    Showing page <span class="font-medium">{currentPage}</span> of <span class="font-medium">{totalPages}</span>
                  </p>
                </div>
                <div>
                  <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px">
                    <button
                      onclick={prevPage}
                      disabled={currentPage === 1}
                      class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Previous
                    </button>
                    
                    {#each Array.from({length: Math.min(5, totalPages)}, (_, i) => i + 1) as page}
                      <button
                        onclick={() => goToPage(page)}
                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium
                          {currentPage === page 
                            ? 'z-10 bg-blue-50 border-blue-500 text-blue-600' 
                            : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'}"
                      >
                        {page}
                      </button>
                    {/each}
                    
                    <button
                      onclick={nextPage}
                      disabled={currentPage === totalPages}
                      class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      Next
                    </button>
                  </nav>
                </div>
              </div>
            </div>
          {/if}
        </div>

        <!-- Note about GraphQL -->
        <div class="mt-6 bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="flex">
            <div class="flex-shrink-0">
              <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
            </div>
            <div class="ml-3">
              <h3 class="text-sm font-medium text-green-800">Real Data Connected</h3>
              <div class="mt-2 text-sm text-green-700">
                <p>This page is now connected to the GraphQL API and showing real user data from the database.</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  {/snippet}
</ProtectedRoute>
