<!-- +error.svelte - Global error page -->
<script lang="ts">
	import { page } from '$app/stores';
	import { dev } from '$app/environment';
	
	$: status = $page.status;
	$: message = $page.error?.message || 'An error occurred';
</script>

<svelte:head>
	<title>{status} - SourceFlex</title>
</svelte:head>

<div class="min-h-screen flex items-center justify-center bg-gray-50">
	<div class="max-w-md w-full text-center space-y-6 p-8">
		<div class="space-y-2">
			<h1 class="text-6xl font-bold text-gray-900">{status}</h1>
			
			{#if status === 404}
				<h2 class="text-xl font-semibold text-gray-700">Page Not Found</h2>
				<p class="text-gray-600">
					The page you're looking for doesn't exist or has been moved.
				</p>
			{:else if status === 403}
				<h2 class="text-xl font-semibold text-gray-700">Access Denied</h2>
				<p class="text-gray-600">
					You don't have permission to access this resource.
				</p>
			{:else if status === 500}
				<h2 class="text-xl font-semibold text-gray-700">Server Error</h2>
				<p class="text-gray-600">
					Something went wrong on our end. Please try again later.
				</p>
			{:else}
				<h2 class="text-xl font-semibold text-gray-700">Error {status}</h2>
				<p class="text-gray-600">{message}</p>
			{/if}
		</div>
		
		{#if dev && $page.error}
			<details class="text-left bg-red-50 p-4 rounded-md">
				<summary class="cursor-pointer text-red-800 font-medium">
					Development Error Details
				</summary>
				<pre class="mt-2 text-sm text-red-700 whitespace-pre-wrap">
					{$page.error.stack || message}
				</pre>
			</details>
		{/if}
		
		<div class="space-y-3">
			<button 
				onclick={() => history.back()} 
				class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
			>
				Go Back
			</button>
			
			<a 
				href="/" 
				class="block w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
			>
				Return Home
			</a>
		</div>
	</div>
</div>
