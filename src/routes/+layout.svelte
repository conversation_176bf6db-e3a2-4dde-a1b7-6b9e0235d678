<script lang="ts">
	import '../app.css';
	import { Toaster } from 'svelte-5-french-toast';
	import SessionWarning from '$lib/components/SessionWarning.svelte';
	import { page } from '$app/stores';
	import { fade } from 'svelte/transition';
	import { onMount } from 'svelte';
	import { initTheme } from '$lib/stores/theme';
	
	let { children } = $props();
	let currentPath = $state('');
	
	// Initialize theme system on mount
	onMount(() => {
		initTheme();
	});
	
	// Update current path when page changes to trigger transition
	$effect(() => {
		if (currentPath !== $page.url.pathname) {
			currentPath = $page.url.pathname;
		}
	});
</script>

<div data-path={currentPath} in:fade={{ duration: 150, delay: 50 }} out:fade={{ duration: 100 }}>
	{@render children()}
</div>

<!-- Session Warning (global) -->
<SessionWarning />

<!-- Toast notifications -->
<Toaster 
	position="top-right"
	toastOptions={{
		duration: 4000,
		style: 'border-radius: 8px; background: #fff; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);'
	}}
/>
