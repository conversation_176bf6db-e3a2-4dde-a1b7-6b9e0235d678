<script lang="ts">
  import ProtectedRoute from '$lib/components/ProtectedRoute.svelte';
  import AppLayout from '$lib/components/layouts/AppLayout.svelte';
  import DeskSwitcher from '$lib/components/DeskSwitcher.svelte';
  import { Button } from '$lib/components/ui/button';
  import { session, userProfile } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';

  let companyName = $state('');
  let timezone = $state('UTC');
  let isLoading = $state(false);
  let isSaving = $state(false);

  // Common timezones
  const timezones = [
    'UTC',
    'America/New_York',
    'America/Chicago', 
    'America/Denver',
    'America/Los_Angeles',
    'Europe/London',
    'Europe/Paris',
    'Asia/Tokyo',
    'Asia/Shanghai',
    'Australia/Sydney'
  ];

  // Initialize form data when profile loads
  $effect(() => {
    if ($userProfile) {
      companyName = $userProfile.company_name || '';
      timezone = $userProfile.timezone || 'UTC';
    }
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();
    isSaving = true;

    try {
      // TODO: Implement GraphQL mutation to update profile
      console.log('Updating profile:', {
        company_name: companyName,
        timezone: timezone,
        onboarding_completed: true
      });

      // Update local store
      userProfile.update(profile => profile ? {
        ...profile,
        company_name: companyName,
        timezone: timezone,
        onboarding_completed: true
      } : null);

      toast.success('Profile updated successfully!');
      
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Failed to update profile');
    } finally {
      isSaving = false;
    }
  }

  async function handleDeskChange(newDesk: 'recruitment' | 'bench_sales') {
    try {
      // TODO: Implement GraphQL mutation
      console.log('Updating desk to:', newDesk);
      
      userProfile.update(profile => profile ? {...profile, current_desk: newDesk} : null);
      toast.success(`Switched to ${newDesk === 'recruitment' ? 'Recruitment' : 'Bench Sales'} desk`);
    } catch (error) {
      console.error('Error updating desk:', error);
      toast.error('Failed to switch desk');
    }
  }
</script>

<svelte:head>
  <title>Profile - SourceFlex</title>
</svelte:head>

<ProtectedRoute>
  {#snippet children()}
    <AppLayout>
      {#snippet children()}
        <div class="p-6">
          <!-- Page Header -->
          <div class="mb-6">
            <h1 class="text-2xl font-bold text-foreground">Profile Settings</h1>
            <p class="text-muted-foreground">Manage your account settings and preferences</p>
          </div>

          <div class="max-w-2xl space-y-6">
            <!-- Personal Information Card -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <h2 class="text-lg font-medium mb-6">Personal Information</h2>

              <form onsubmit={handleSubmit} class="space-y-6">
                <!-- Email (read-only) -->
                <div class="space-y-2">
                  <label for="email" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Email Address
                  </label>
                  <input
                    id="email"
                    type="email"
                    value={$session?.user?.email || ''}
                    readonly
                    class="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground cursor-not-allowed"
                  />
                  <p class="text-xs text-muted-foreground">
                    Email cannot be changed. Contact support if needed.
                  </p>
                </div>

                <!-- Company Name -->
                <div class="space-y-2">
                  <label for="company" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Company Name
                  </label>
                  <input
                    id="company"
                    type="text"
                    bind:value={companyName}
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    placeholder="Enter your company name"
                  />
                </div>

                <!-- Timezone -->
                <div class="space-y-2">
                  <label for="timezone" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Timezone
                  </label>
                  <select
                    id="timezone"
                    bind:value={timezone}
                    class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    {#each timezones as tz}
                      <option value={tz}>{tz}</option>
                    {/each}
                  </select>
                </div>

                <!-- Current Desk -->
                <div class="space-y-2">
                  <label for="desk-switcher" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Current Desk
                  </label>
                  <div id="desk-switcher" class="pt-2">
                    <DeskSwitcher onDeskChange={handleDeskChange} />
                  </div>
                </div>

                <!-- Email Domain (read-only) -->
                <div class="space-y-2">
                  <label for="email-domain" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                    Email Domain
                  </label>
                  <input
                    id="email-domain"
                    type="text"
                    value={$userProfile?.email_domain || 'N/A'}
                    readonly
                    class="flex h-10 w-full rounded-md border border-input bg-muted px-3 py-2 text-sm text-muted-foreground cursor-not-allowed"
                  />
                  <p class="text-xs text-muted-foreground">
                    Automatically extracted from your email address.
                  </p>
                </div>

                <!-- Save Button -->
                <div class="flex justify-end">
                  <Button type="submit" disabled={isSaving}>
                    {#if isSaving}
                      <svg class="animate-spin -ml-1 mr-3 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Saving...
                    {:else}
                      Save Changes
                    {/if}
                  </Button>
                </div>
              </form>
            </div>

            <!-- Profile Status Card -->
            <div class="rounded-lg border bg-card text-card-foreground shadow-sm p-6">
              <h3 class="text-lg font-medium mb-4">Profile Status</h3>
              
              <div class="space-y-3">
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">Account Status</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Active
                  </span>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">Email Verified</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    Verified
                  </span>
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">Onboarding</span>
                  {#if $userProfile?.onboarding_completed}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                      Completed
                    </span>
                  {:else}
                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                      Pending
                    </span>
                  {/if}
                </div>
                
                <div class="flex items-center justify-between">
                  <span class="text-sm text-muted-foreground">User Role</span>
                  <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                    {$session?.user?.defaultRole || 'user'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      {/snippet}
    </AppLayout>
  {/snippet}
</ProtectedRoute>
