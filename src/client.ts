import { HoudiniClient } from '$houdini';
import { browser } from '$app/environment';
import { nhost } from './lib/stores/nhost';

export const houdiniClient = new HoudiniClient({
  url: 'https://pttthnqikxdsxmeccqho.graphql.us-west-2.nhost.run/v1',
  fetchParams() {
    // Only get session in browser environment
    if (!browser) {
      return { headers: {} };
    }
    
    // Get the current session token for auth
    const session = nhost?.auth?.getSession ? nhost.auth.getSession() : null;
    
    return {
      headers: {
        ...(session?.accessToken && {
          Authorization: `Bearer ${session.accessToken}`
        })
      }
    };
  }
});

// Export the client as default
export default houdiniClient;
