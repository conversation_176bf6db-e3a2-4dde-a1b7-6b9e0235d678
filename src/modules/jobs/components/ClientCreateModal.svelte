<script lang="ts">
  import { createForm } from 'sveltekit-superforms';
  import { superForm } from 'sveltekit-superforms';
  import { createClientSchema, type CreateClientValues } from '../schemas/createJobSchema';
  import { CreateClientStore } from '$houdini';
  import { toast } from 'svelte-5-french-toast';
  
  import * as Dialog from '$lib/components/ui/dialog';
  import * as Form from '$lib/components/ui/form';
  import { Input } from '$lib/components/ui/input';
  import { Button } from '$lib/components/ui/button';
  import { Textarea } from '$lib/components/ui/textarea';
  import { Loader2 } from 'lucide-svelte';
  
  // Props
  interface ClientCreateModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    onClientCreated: (client: { id: string; name: string }) => void;
  }
  
  const { open = false, onOpenChange, onClientCreated } = $props<ClientCreateModalProps>();
  
  // Create the form
  const form = createForm(createClientSchema, {
    resetForm: true,
    validators: {
      name: (value: string | undefined) => {
        if (!value) return 'Client name is required';
        return null;
      }
    }
  });
  
  // Initialize the form with SuperForms
  const { form: clientForm, errors, enhance, submitting, reset } = superForm(form, {
    resetForm: true,
    onSubmit: ({ formData, cancel }: { formData: FormData; cancel: () => void }) => {
      // If the form is already submitting, prevent duplicate submissions
      if ($submitting) {
        cancel();
      }
    },
    onResult: ({ result }: { result: { type: string } }) => {
      // Handle form submission result
      if (result.type === 'success') {
        // Form validation succeeded, now submit to GraphQL
        submitClient();
      }
    }
  });
  
  // Create client mutation store
  const createClientStore = new CreateClientStore();
  

  
  // Submit client to GraphQL
  async function submitClient() {
    try {
      // Get form values
      const clientData: CreateClientValues = $clientForm;
      
      // Execute the mutation
      const result = await createClientStore.mutate({
        client: {
          name: clientData.name,
          contact_email: clientData.contact_email || null,
          contact_phone: clientData.contact_phone || null,
          location: clientData.location || null,
          industry: clientData.industry || null,
          description: clientData.description || null,
          website: clientData.website || null,
          created_at: new Date().toISOString(),
          is_active: true
        }
      });
      
      // Check for errors
      if (result.errors) {
        throw new Error(result.errors[0].message);
      }
      
      // Get the created client
      const createdClient = result.data?.insert_clients_one;
      
      if (createdClient) {
        // Notify parent component
        onClientCreated({
          id: createdClient.id,
          name: createdClient.name
        });
        
        // Show success message
        toast.success(`Client ${createdClient.name} created successfully`);
        
        // Close the modal
        handleOpenChange(false);
      }
    } catch (error) {
      console.error('Error creating client:', error);
      toast.error('Failed to create client. Please try again.');
    }
  }
  
  // Handle dialog close
  function handleOpenChange(isOpen: boolean) {
    if (!isOpen && !$submitting) {
      reset();
    }
    onOpenChange(isOpen);
  }
</script>

<Dialog.Root {open} onOpenChange={handleOpenChange}>
  <Dialog.Content class="sm:max-w-[500px]">
    <Dialog.Header>
      <Dialog.Title>Add New Client</Dialog.Title>
      <Dialog.Description>
        Create a new client to associate with this job.
      </Dialog.Description>
    </Dialog.Header>
    
    <form method="POST" use:enhance>
      <div class="grid gap-4 py-4">
        <Form.Field {errors} name="name">
          <Form.Label>Client Name <span class="text-destructive">*</span></Form.Label>
          <Form.Input>
            <Input 
              name="name" 
              bind:value={$clientForm.name} 
              placeholder="Enter client name" 
              required 
            />
          </Form.Input>
          <Form.FieldErrors />
        </Form.Field>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Form.Field {errors} name="contact_email">
            <Form.Label>Contact Email</Form.Label>
            <Form.Input>
              <Input 
                name="contact_email" 
                bind:value={$clientForm.contact_email} 
                placeholder="<EMAIL>" 
                type="email" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <Form.Field {errors} name="contact_phone">
            <Form.Label>Contact Phone</Form.Label>
            <Form.Input>
              <Input 
                name="contact_phone" 
                bind:value={$clientForm.contact_phone} 
                placeholder="(*************" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
        </div>
        
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Form.Field {errors} name="location">
            <Form.Label>Location</Form.Label>
            <Form.Input>
              <Input 
                name="location" 
                bind:value={$clientForm.location} 
                placeholder="City, State" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
          
          <Form.Field {errors} name="industry">
            <Form.Label>Industry</Form.Label>
            <Form.Input>
              <Input 
                name="industry" 
                bind:value={$clientForm.industry} 
                placeholder="e.g. Technology" 
              />
            </Form.Input>
            <Form.FieldErrors />
          </Form.Field>
        </div>
        
        <Form.Field {errors} name="website">
          <Form.Label>Website</Form.Label>
          <Form.Input>
            <Input 
              name="website" 
              bind:value={$clientForm.website} 
              placeholder="https://example.com" 
            />
          </Form.Input>
          <Form.FieldErrors />
        </Form.Field>
        
        <Form.Field {errors} name="description">
          <Form.Label>Description</Form.Label>
          <Form.Input>
            <Textarea 
              name="description" 
              bind:value={$clientForm.description} 
              placeholder="Brief description of the client" 
              rows="3" 
            />
          </Form.Input>
          <Form.FieldErrors />
        </Form.Field>
      </div>
      
      <Dialog.Footer>
        <Button type="button" variant="outline" on:click={() => handleOpenChange(false)} disabled={$submitting}>
          Cancel
        </Button>
        <Button type="submit" disabled={$submitting}>
          {#if $submitting}
            <Loader2 class="mr-2 h-4 w-4 animate-spin" />
            Creating...
          {:else}
            Create Client
          {/if}
        </Button>
      </Dialog.Footer>
    </form>
  </Dialog.Content>
</Dialog.Root>