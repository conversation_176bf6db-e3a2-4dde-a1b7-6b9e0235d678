<script lang="ts">
  import { FlexRender } from '$lib/components/ui/data-table';
  import * as Table from '$lib/components/ui/table';
  import { Button } from '$lib/components/ui/button';
  import { GetJobsStore } from '$houdini';
  import type { GetJobs$result } from '$houdini';
  
  // Props
  interface JobsDataTableProps {
    onJobClick?: (jobId: string) => void;
  }
  
  const { onJobClick } = $props<JobsDataTableProps>();
  
  // Houdini query store
  const getJobsStore = new GetJobsStore();
  
  // Fetch jobs on component mount
  getJobsStore.fetch();
  
  // Reactive data from <PERSON><PERSON><PERSON> using runes
  const jobs = $derived($getJobsStore.data?.jobs || []);
  const totalCount = $derived($getJobsStore.data?.jobs_aggregate?.aggregate?.count || 0);
  const isLoading = $derived($getJobsStore.fetching);
  
  // Debug logging
  console.log('JobsDataTable - Component mounted');
  console.log('JobsDataTable - Store data:', $getJobsStore.data);
  console.log('JobsDataTable - Jobs:', jobs);
  console.log('JobsDataTable - Loading:', isLoading);
  
  // Format compensation range
  function formatCompensation(job: NonNullable<GetJobs$result['jobs']>[0]) {
    if (!job.min_compensation && !job.max_compensation) return '-';
    
    const format = (amount: number) => {
      return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: job.currency || 'USD',
        minimumFractionDigits: 0,
        maximumFractionDigits: 0
      }).format(amount);
    };
    
    if (job.min_compensation && job.max_compensation) {
      return `${format(job.min_compensation)} - ${format(job.max_compensation)}`;
    }
    
    return format(job.min_compensation || job.max_compensation || 0);
  }
  
  // Format status badge
  function getStatusBadgeClass(status: string) {
    const statusClasses = {
      draft: 'bg-gray-100 text-gray-800',
      open: 'bg-green-100 text-green-800',
      on_hold: 'bg-yellow-100 text-yellow-800',
      filled: 'bg-blue-100 text-blue-800',
      closed: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800'
    };
    return statusClasses[status as keyof typeof statusClasses] || 'bg-gray-100 text-gray-800';
  }
  
  // Format priority badge
  function getPriorityBadgeClass(priority: string) {
    const priorityClasses = {
      A: 'bg-red-100 text-red-800',
      B: 'bg-orange-100 text-orange-800', 
      C: 'bg-yellow-100 text-yellow-800',
      D: 'bg-green-100 text-green-800'
    };
    return priorityClasses[priority as keyof typeof priorityClasses] || 'bg-gray-100 text-gray-800';
  }
  
  // Handle row click
  function handleRowClick(job: NonNullable<GetJobs$result['jobs']>[0]) {
    if (onJobClick) {
      onJobClick(job.id);
    }
  }
</script>

<div class="space-y-4">
  <!-- Loading State -->
  {#if isLoading}
    <div class="flex items-center justify-center py-8">
      <div class="text-sm text-muted-foreground">Loading jobs...</div>
    </div>
  {:else if jobs.length === 0}
    <!-- Empty State -->
    <div class="rounded-lg border bg-card text-card-foreground shadow-sm">
      <div class="flex flex-col items-center justify-center py-12 px-6">
        <div class="h-12 w-12 bg-muted rounded-lg flex items-center justify-center mb-4">
          <svg class="h-6 w-6 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H8a2 2 0 01-2-2V8a2 2 0 012-2V6" />
          </svg>
        </div>
        <h3 class="text-lg font-semibold mb-2">No jobs found</h3>
        <p class="text-muted-foreground text-center mb-4">
          You haven't created any job listings yet. Get started by adding your first job.
        </p>
        <Button variant="default">
          <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Create Your First Job
        </Button>
      </div>
    </div>
  {:else}
    <!-- Jobs Table -->
    <div class="rounded-lg border bg-card">
      <Table.Root>
        <Table.Header>
          <Table.Row>
            <Table.Head>Job Title</Table.Head>
            <Table.Head>Client</Table.Head>
            <Table.Head>Status</Table.Head>
            <Table.Head>Priority</Table.Head>
            <Table.Head>Location</Table.Head>
            <Table.Head>Compensation</Table.Head>
            <Table.Head>Created</Table.Head>
          </Table.Row>
        </Table.Header>
        <Table.Body>
          {#each jobs as job (job.id)}
            <Table.Row 
              class="cursor-pointer hover:bg-muted/50" 
              onclick={() => handleRowClick(job)}
            >
              <Table.Cell class="font-medium">{job.title}</Table.Cell>
              <Table.Cell>{job.client?.name || '-'}</Table.Cell>
              <Table.Cell>
                <span class="px-2 py-1 rounded-full text-xs font-medium {getStatusBadgeClass(job.status)}">
                  {job.status.replace('_', ' ').toUpperCase()}
                </span>
              </Table.Cell>
              <Table.Cell>
                <span class="px-2 py-1 rounded-full text-xs font-medium {getPriorityBadgeClass(job.priority)}">
                  {job.priority}
                </span>
              </Table.Cell>
              <Table.Cell>{job.primary_address || 'Remote'}</Table.Cell>
              <Table.Cell>{formatCompensation(job)}</Table.Cell>
              <Table.Cell class="text-muted-foreground">
                {new Date(job.created_at).toLocaleDateString()}
              </Table.Cell>
            </Table.Row>
          {/each}
        </Table.Body>
      </Table.Root>
    </div>
    
    <!-- Table Footer with Count -->
    <div class="flex items-center justify-between text-sm text-muted-foreground">
      <div>Showing {jobs.length} of {totalCount} jobs</div>
    </div>
  {/if}
</div>
