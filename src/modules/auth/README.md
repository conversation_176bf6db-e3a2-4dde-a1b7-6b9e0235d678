# SourceFlex Authentication Module

## Overview
Complete authentication system for SourceFlex using nHost backend with SvelteKit frontend.

## Features Implemented ✅

### 1. Authentication Pages
- `/` - Main auth page with login/register tabs
- `/forgotpassword` - Password reset email sender
- `/resetpassword` - New password form with token validation
- `/resendverification` - Resend email verification
- `/verify` - Email verification page

### 2. Protected Routes
- `/dashboard` - Main dashboard (requires authentication)
- `/profile` - User profile management (requires authentication)
- `/org/users` - Organization user management (requires org_manager role)
- `/admin/users` - System-wide user management (requires admin role)

### 3. Core Components
- `ProtectedRoute.svelte` - Route protection with role-based access
- `DeskSwitcher.svelte` - Toggle between recruitment/bench_sales desks

### 4. Authentication Store System
- `auth.ts` - Main authentication state management
- `nhost.ts` - nHost client configuration
- Real-time auth state synchronization
- User profile management

### 5. Role-Based Access Control
- **user** - Basic access to dashboard and profile
- **org_manager** - Access to organization user management
- **admin** - Full system access including all users

## Technical Stack

### Dependencies
- `@nhost/nhost-js` - nHost authentication client
- `svelte-5-french-toast` - Toast notifications
- `lucide-svelte` - Icons
- `zod` + `sveltekit-superforms` - Form validation (ready for use)

### GraphQL Queries
- User profile management queries in `/lib/graphql/`
- Organization user queries for role-based access
- Auto profile creation on registration

## Environment Variables Required
```env
VITE_NHOST_SUBDOMAIN=pttthnqikxdsxmeccqho
VITE_NHOST_REGION=us-west-2
VITE_GRAPHQL_ENDPOINT=https://pttthnqikxdsxmeccqho.graphql.us-west-2.nhost.run/v1
VITE_APP_NAME=SourceFlex
VITE_APP_URL=http://localhost:5173
NODE_ENV=development
```

## File Structure Created
```
src/
├── lib/
│   ├── components/
│   │   ├── DeskSwitcher.svelte
│   │   └── ProtectedRoute.svelte
│   ├── graphql/
│   │   ├── user-profile.gql
│   │   └── org-users.gql
│   └── stores/
│       ├── auth.ts
│       └── nhost.ts
└── routes/
    ├── (auth)/
    │   ├── +layout.svelte
    │   ├── +page.svelte
    │   ├── forgotpassword/+page.svelte
    │   ├── resetpassword/+page.svelte
    │   ├── resendverification/+page.svelte
    │   └── verify/+page.svelte
    ├── dashboard/+page.svelte
    ├── profile/+page.svelte
    ├── org/
    │   └── users/+page.svelte
    ├── admin/
    │   └── users/+page.svelte
    ├── unauthorized/+page.svelte
    ├── +layout.svelte (with Toaster)
    └── +page.svelte (auto-redirect)
```

## Key Features

### Auto Profile Creation
- Email domain extraction (removes subdomains)
- Default desk assignment to 'recruitment'
- Company name from registration form
- Onboarding completion tracking

### Session Management
- 30-minute idle timeout with activity tracking
- Automatic session termination on browser close
- Comprehensive browser storage clearing on logout
- Session validation on application startup
- Enhanced security with disabled auto-refresh tokens

### User Experience
- Loading states for all async operations
- Toast notifications for user feedback
- Mobile-responsive design
- Accessibility considerations

## Next Steps (TODO)

### 1. Connect GraphQL Queries
Replace mock data with actual Houdini GraphQL calls:
- Update `loadUserProfile()` in dashboard
- Implement `loadOrgUsers()` in org/users
- Implement `loadAllUsers()` in admin/users
- Add profile update mutations

### 2. nHost Backend Configuration
- Set up user_profiles table with proper permissions
- Configure JWT claims for org_manager role
- Set up email verification templates
- Configure password reset flow

### 3. Enhanced Features
- Bulk user management for admins
- Organization assignment for org_managers
- User role modification interface
- Audit logging for admin actions

### 4. Form Validation
- Implement Superforms with Zod schemas
- Add client-side validation feedback
- Improve error handling and messaging

## Testing Checklist
- [ ] User registration flow
- [ ] Email verification process
- [ ] Login/logout functionality
- [ ] Password reset flow
- [ ] Role-based route access
- [ ] Desk switching functionality
- [ ] Profile updates
- [ ] Responsive design
- [ ] Error handling

## Security Considerations
- All routes properly protected with enhanced validation
- Role-based access control implemented
- Session tokens managed securely via nHost
- Comprehensive browser storage clearing on logout
- 30-minute session timeout for security
- Auto-refresh tokens disabled to prevent session persistence
- Complete storage clearing (localStorage, sessionStorage, IndexedDB, cookies)
- Session validation on startup prevents authentication bypass
- HTTPS required for production
- CSRF protection via SvelteKit
