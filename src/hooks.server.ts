import type { Handle } from '@sveltejs/kit';
import { handlePreClearance } from '$lib/middleware/turnstile';

export const handle: Handle = async ({ event, resolve }) => {
  const hostname = event.url.hostname;
  const pathname = event.url.pathname;
  
  // Skip processing for browser/devtools requests that shouldn't be logged as errors
  const skipLogging = [
    '/.well-known/',
    '/favicon.ico',
    '/robots.txt',
    '/sitemap.xml'
  ];
  
  const shouldSkipLogging = skipLogging.some(path => pathname.startsWith(path));

  // Simple domain-based routing
  if (hostname === 'sourceflex.io') {
    // Main domain: Redirect auth routes to app subdomain
    if (pathname.startsWith('/auth') || pathname.startsWith('/forgot')) {
      return new Response(null, {
        status: 302,
        headers: { Location: `https://app.sourceflex.io${pathname}` }
      });
    }
  }
  
  // For localhost - no redirects, handle everything locally
  if (hostname === 'localhost' || hostname === '127.0.0.1') {
    // Allow all routes on localhost for development
    // No redirects needed
  }
  
  // Handle Turnstile pre-clearance for protected routes
  const preClearanceResponse = await handlePreClearance(event);
  if (preClearanceResponse) {
    return preClearanceResponse;
  }

  const response = await resolve(event, {
    filterSerializedResponseHeaders: (name) => {
      // Allow CORS headers and security headers
      return name.startsWith('x-') || name === 'content-security-policy';
    }
  });

  // Security headers with Turnstile support
  response.headers.set('X-Frame-Options', 'DENY');
  response.headers.set('X-Content-Type-Options', 'nosniff');

  // Enhanced CSP for nHost + Turnstile pre-clearance
  response.headers.set('Content-Security-Policy', [
    "default-src 'self'",
    "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://challenges.cloudflare.com",
    "style-src 'self' 'unsafe-inline'",
    "connect-src 'self' https://*.nhost.run https://*.graphql.us-west-2.nhost.run https://challenges.cloudflare.com",
    "frame-src https://challenges.cloudflare.com",
    "img-src 'self' data: https:",
    "font-src 'self' https:"
  ].join('; '));

  return response;
};
