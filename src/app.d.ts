// See https://svelte.dev/docs/kit/types#app.d.ts
// for information about these interfaces

declare global {
	namespace App {
		// interface Error {}
		interface Locals {
			user?: {
				id: string;
				email: string;
				[key: string]: any;
			} | null;
		}
		// interface PageData {}
		// interface PageState {}
		interface Platform {
			env?: {
				// Add your Cloudflare bindings here
				// Example: YOUR_KV_NAMESPACE: KVNamespace;
				// Example: YOUR_DURABLE_OBJECT_NAMESPACE: DurableObjectNamespace;
				TURNSTILE_SECRET_KEY?: string;
				NHOST_ADMIN_SECRET?: string;
			};
			ctx?: ExecutionContext;
			caches?: CacheStorage;
			cf?: IncomingRequestCfProperties;
		}
	}

	// Cloudflare Turnstile types are defined in src/lib/types/turnstile.ts
}

// Manual type declarations for SvelteKit $app modules
declare module '$app/forms' {
	export function enhance(
		form: HTMLFormElement,
		submit?: (input: {
			formData: FormData;
			formElement: HTMLFormElement;
			action: URL;
			cancel(): void;
			submitter: HTMLElement | null;
		}) => void | ((opts: { result: unknown; update: (opts?: { reset?: boolean }) => void }) => void)
	): { destroy(): void };
}

declare module '$app/navigation' {
	export function goto(url: string | URL, opts?: { replaceState?: boolean; noScroll?: boolean; keepFocus?: boolean; invalidateAll?: boolean; state?: unknown }): Promise<void>;
	export function invalidate(dependency: string | URL | ((url: URL) => boolean)): Promise<void>;
	export function invalidateAll(): Promise<void>;
	export function preloadData(href: string): Promise<void>;
	export function preloadCode(...urls: string[]): Promise<void>;
	export function beforeNavigate(fn: (navigation: { from: URL | null; to: URL | null; type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate'; willUnload: boolean; cancel(): void }) => void): void;
	export function afterNavigate(fn: (navigation: { from: URL | null; to: URL | null; type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate' }) => void): void;
	export function onNavigate(fn: (navigation: { from: URL | null; to: URL | null; type: 'enter' | 'leave' | 'link' | 'goto' | 'popstate'; willUnload: boolean; complete: Promise<void> }) => void | (() => void)): void;
	export function disableScrollHandling(): void;
	export function pushState(url: string | URL, state: unknown): void;
	export function replaceState(url: string | URL, state: unknown): void;
}

// Environment variable type declarations for SvelteKit
declare module '$env/static/public' {
	export const PUBLIC_NHOST_SUBDOMAIN: string;
	export const PUBLIC_NHOST_REGION: string;
	export const PUBLIC_GRAPHQL_ENDPOINT: string;
	export const PUBLIC_APP_NAME: string;
	export const PUBLIC_TURNSTILE_SITE_KEY: string;
}

declare module '$env/static/private' {
	export const TURNSTILE_SECRET_KEY: string;
	export const NHOST_ADMIN_SECRET: string;
	export const NODE_ENV: string;
}

declare module '$env/dynamic/private' {
	export const env: {
		TURNSTILE_SECRET_KEY?: string;
		NHOST_ADMIN_SECRET?: string;
		NODE_ENV?: string;
		[key: string]: string | undefined;
	};
}

declare module '$env/dynamic/public' {
	export const env: {
		PUBLIC_NHOST_SUBDOMAIN?: string;
		PUBLIC_NHOST_REGION?: string;
		PUBLIC_GRAPHQL_ENDPOINT?: string;
		PUBLIC_APP_NAME?: string;
		PUBLIC_TURNSTILE_SITE_KEY?: string;
		[key: string]: string | undefined;
	};
}

export {};
