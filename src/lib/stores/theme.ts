import { browser } from '$app/environment';
import { writable } from 'svelte/store';

type Theme = 'light' | 'dark' | 'system';

// Create reactive theme store
export const theme = writable<Theme>('system');

// System preference detection
function getSystemTheme(): 'light' | 'dark' {
  if (!browser) return 'light';
  return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
}

// Apply theme to document
function applyTheme(selectedTheme: Theme) {
  if (!browser) return;
  
  const root = document.documentElement;
  const actualTheme = selectedTheme === 'system' ? getSystemTheme() : selectedTheme;
  
  // Remove existing theme classes
  root.classList.remove('light', 'dark');
  
  // Apply new theme
  root.classList.add(actualTheme);
  
  // Store preference (except for system which is default)
  if (selectedTheme !== 'system') {
    localStorage.setItem('sourceflex-theme', selectedTheme);
  } else {
    localStorage.removeItem('sourceflex-theme');
  }
}

// Initialize theme system
export function initTheme() {
  if (!browser) return;
  
  // Get stored preference or default to system
  const storedTheme = localStorage.getItem('sourceflex-theme') as Theme;
  const initialTheme = storedTheme || 'system';
  
  // Set store value
  theme.set(initialTheme);
  
  // Apply initial theme
  applyTheme(initialTheme);
  
  // Listen for system theme changes
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  mediaQuery.addEventListener('change', () => {
    theme.update(currentTheme => {
      if (currentTheme === 'system') {
        applyTheme('system');
      }
      return currentTheme;
    });
  });
  
  // Subscribe to theme store changes
  theme.subscribe(applyTheme);
}

// Manual theme switching functions
export function setLightTheme() {
  theme.set('light');
}

export function setDarkTheme() {
  theme.set('dark');
}

export function setSystemTheme() {
  theme.set('system');
}

// Get current effective theme (resolves 'system' to actual theme)
export function getCurrentTheme(): 'light' | 'dark' {
  if (!browser) return 'light';
  const currentTheme = theme;
  return getSystemTheme(); // Always check current system preference
}