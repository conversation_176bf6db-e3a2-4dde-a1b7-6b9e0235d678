import { NhostClient } from '@nhost/nhost-js';
import { browser } from '$app/environment';

// Get environment variables - works in both dev and Cloudflare Workers
const getEnvVar = (key: string, fallback: string = '') => {
  if (browser) {
    // Client-side: use import.meta.env
    return import.meta.env[key] || fallback;
  }
  // Server-side: try different sources for Cloudflare Workers
  if (typeof globalThis !== 'undefined' && globalThis.process?.env) {
    return globalThis.process.env[key] || fallback;
  }
  return fallback;
};

const NHOST_SUBDOMAIN = getEnvVar('PUBLIC_NHOST_SUBDOMAIN', 'pttthnqikxdsxmeccqho');
const NHOST_REGION = getEnvVar('PUBLIC_NHOST_REGION', 'us-west-2');

// Function to create a fresh nHost client
const createNhostClient = () => browser 
  ? new NhostClient({
      subdomain: NHOST_SUBDOMAIN,
      region: NHOST_REGION,
      autoRefreshToken: false
    }) 
  : {
      // Safe mock for SSR
      auth: {
        getSession: () => null,
        onAuthStateChanged: () => {},
        signUp: () => Promise.resolve({ session: null, error: null }),
        signIn: () => Promise.resolve({ session: null, error: null }),
        signOut: () => Promise.resolve({ error: null }),
        resetPassword: () => Promise.resolve({ error: null }),
        changePassword: () => Promise.resolve({ error: null }),
        refreshSession: () => Promise.resolve({ session: null, error: null })
      }
    } as any;

// Create initial nHost client
export let nhost = createNhostClient();

// Function to recreate nHost client (nuclear option for auth issues)
export const recreateNhostClient = () => {
  console.log('🚨 RECREATING NHOST CLIENT - Nuclear option');
  nhost = createNhostClient();
  return nhost;
};
