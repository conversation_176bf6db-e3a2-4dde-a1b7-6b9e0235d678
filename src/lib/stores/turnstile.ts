/**
 * Shared Turnstile State Management for Auth Routes
 * Handles token persistence and verification status across auth pages
 */

import { writable } from 'svelte/store';
import { browser } from '$app/environment';

interface TurnstileState {
  token: string | null;
  isVerified: boolean;
  expiresAt: number | null;
}

const STORAGE_KEY = 'turnstile_auth_state';
const TOKEN_DURATION = 30 * 60 * 1000; // 30 minutes

// Create initial state
function createTurnstileState() {
  const initialState: TurnstileState = {
    token: null,
    isVerified: false,
    expiresAt: null
  };

  const { subscribe, set, update } = writable(initialState);

  return {
    subscribe,
    
    // Initialize from storage
    init: () => {
      if (!browser) return;
      
      try {
        const stored = sessionStorage.getItem(STORAGE_KEY);
        if (stored) {
          const state: TurnstileState = JSON.parse(stored);
          
          // Check if token is still valid
          if (state.expiresAt && Date.now() < state.expiresAt && state.token) {
            set({
              token: state.token,
              isVerified: true,
              expiresAt: state.expiresAt
            });
            return true; // Token is valid
          }
        }
      } catch (error) {
        console.error('Error loading Turnstile state:', error);
      }
      
      return false; // No valid token
    },

    // Set verification success
    setVerified: (token: string) => {
      const expiresAt = Date.now() + TOKEN_DURATION;
      const newState: TurnstileState = {
        token,
        isVerified: true,
        expiresAt
      };
      
      set(newState);
      
      if (browser) {
        try {
          sessionStorage.setItem(STORAGE_KEY, JSON.stringify(newState));
        } catch (error) {
          console.error('Error saving Turnstile state:', error);
        }
      }
    },

    // Clear verification
    clear: () => {
      set({
        token: null,
        isVerified: false,
        expiresAt: null
      });
      
      if (browser) {
        try {
          sessionStorage.removeItem(STORAGE_KEY);
        } catch (error) {
          console.error('Error clearing Turnstile state:', error);
        }
      }
    },

    // Check if token is still valid
    isTokenValid: (): boolean => {
      if (!browser) return false;
      
      try {
        const stored = sessionStorage.getItem(STORAGE_KEY);
        if (stored) {
          const state: TurnstileState = JSON.parse(stored);
          return !!(state.expiresAt && Date.now() < state.expiresAt && state.token);
        }
      } catch (error) {
        console.error('Error checking token validity:', error);
      }
      
      return false;
    },

    // Get current token
    getToken: (): string | null => {
      if (!browser) return null;
      
      try {
        const stored = sessionStorage.getItem(STORAGE_KEY);
        if (stored) {
          const state: TurnstileState = JSON.parse(stored);
          if (state.expiresAt && Date.now() < state.expiresAt && state.token) {
            return state.token;
          }
        }
      } catch (error) {
        console.error('Error getting token:', error);
      }
      
      return null;
    }
  };
}

export const turnstileState = createTurnstileState();
