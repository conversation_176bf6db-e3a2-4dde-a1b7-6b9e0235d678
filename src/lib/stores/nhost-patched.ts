// Quick patch for Turnstile integration
// This modifies the nHost client to include Turnstile tokens

import { nhost } from './nhost.js';

// Store the original signUp method
const originalSignUp = nhost.auth.signUp.bind(nhost.auth);

// Override signUp to handle Turnstile tokens
nhost.auth.signUp = function(options) {
  const { turnstileToken, ...restOptions } = options;
  
  if (turnstileToken) {
    // Add Turnstile token as header for nHost
    return originalSignUp({
      ...restOptions,
      options: {
        ...restOptions.options,
        headers: {
          'x-cf-turnstile-response': turnstileToken
        }
      }
    });
  }
  
  return originalSignUp(restOptions);
};

export { nhost };
