import { writable, derived } from 'svelte/store';
import { browser } from '$app/environment';
import { nhost, recreateNhostClient } from './nhost.js';
import type { NhostSession } from '@nhost/nhost-js';
import type { UserProfile, AuthResult, SignUpOptions } from '$lib/types/auth.js';

// Session management flags
let isSigningOut = false;
let authStateListenerActive = true;

// Enhanced session management configuration
const SESSION_CONFIG = {
  IDLE_TIMEOUT: 30 * 60 * 1000, // 30 minutes in milliseconds
  WARNING_TIME: 5 * 60 * 1000,  // Show warning 5 minutes before timeout
  ACTIVITY_EVENTS: ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart']
};

// Session state stores
export const session = writable<NhostSession | null>(null);
export const userProfile = writable<UserProfile | null>(null);
export const sessionWarning = writable<boolean>(false);
export const sessionTimeRemaining = writable<number>(SESSION_CONFIG.IDLE_TIMEOUT);

// Derived stores
export const isAuthenticated = derived(session, ($session) => {
  return $session?.user && !$session?.user?.isAnonymous;
});

export const userRole = derived(session, ($session) => {
  if (!$session?.user) return null;
  return $session.user.defaultRole || 'user';
});

export const userRoles = derived(session, ($session) => {
  if (!$session?.user) return [];
  const roles = ($session.user as any).roles || [];
  return roles;
});

// Session management class
class SessionManager {
  private idleTimer: number | null = null;
  private warningTimer: number | null = null;
  private lastActivity: number = Date.now();
  private warningShown: boolean = false;

  constructor() {
    if (!browser) return;
    
    this.setupActivityListeners();
    this.startTimers();
  }

  private setupActivityListeners() {
    if (!browser || typeof document === 'undefined') return;
    
    SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
      document.addEventListener(event, () => this.updateActivity(), { passive: true });
    });
  }

  private updateActivity() {
    this.lastActivity = Date.now();
    
    if (this.warningShown) {
      this.hideWarning();
    }
    
    this.resetTimers();
  }

  private startTimers() {
    this.resetTimers();
  }

  private resetTimers() {
    // Clear existing timers
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    // Start warning timer
    this.warningTimer = window.setTimeout(() => {
      this.showWarning();
    }, SESSION_CONFIG.IDLE_TIMEOUT - SESSION_CONFIG.WARNING_TIME);
    
    // Start idle timeout timer
    this.idleTimer = window.setTimeout(() => {
      this.handleIdleTimeout();
    }, SESSION_CONFIG.IDLE_TIMEOUT);
    
    // Update remaining time
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private showWarning() {
    this.warningShown = true;
    sessionWarning.set(true);
    
    // Start countdown
    const startTime = Date.now();
    const countdownInterval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = SESSION_CONFIG.WARNING_TIME - elapsed;
      
      if (remaining <= 0) {
        clearInterval(countdownInterval);
        return;
      }
      
      sessionTimeRemaining.set(remaining);
    }, 1000);
  }

  private hideWarning() {
    this.warningShown = false;
    sessionWarning.set(false);
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
  }

  private async handleIdleTimeout() {
    console.log('Session idle timeout - signing out');
    await authActions.signOut();
  }

  public extendSession() {
    this.updateActivity();
  }

  public destroy() {
    if (!browser) return;
    
    if (this.idleTimer) clearTimeout(this.idleTimer);
    if (this.warningTimer) clearTimeout(this.warningTimer);
    
    if (typeof document !== 'undefined') {
      SESSION_CONFIG.ACTIVITY_EVENTS.forEach(event => {
        document.removeEventListener(event, () => this.updateActivity());
      });
    }
  }
}

// Initialize session manager
let sessionManager: SessionManager | null = null;

// Browser initialization
if (browser) {
  const initializeAuth = async () => {
    try {
      console.log('🔄 INITIALIZING AUTH SYSTEM...');
      
      // SECURITY: Always start with completely clean state
      isSigningOut = false;
      authStateListenerActive = true;
      session.set(null);
      userProfile.set(null);
      
      // AGGRESSIVE: Clear any residual storage on startup
      if (browser) {
        try {
          // Don't clear everything, but clear auth-specific items
          const authKeys = Object.keys(localStorage).filter(key => 
            key.includes('nhost') || 
            key.includes('auth') || 
            key.includes('session') ||
            key.includes('token')
          );
          
          authKeys.forEach(key => {
            console.log('🧹 Removing suspicious localStorage key:', key);
            localStorage.removeItem(key);
          });
          
          // Clear sessionStorage completely (it's session-specific anyway)
          sessionStorage.clear();
          
        } catch (error) {
          console.error('❌ Initial storage clear error:', error);
        }
      }
      
      // CRITICAL: Check for any existing session AFTER clearing storage
      const initialSession = nhost.auth.getSession();
      console.log('🔍 Initial session check:', initialSession?.user?.email || 'No session');
      
      if (initialSession?.user) {
        console.log('⚠️ Found existing session, validating with server...');
        
        try {
          // CRITICAL: Verify session is still valid with server
          await nhost.auth.refreshSession();
          const validatedSession = nhost.auth.getSession();
          
          if (validatedSession?.user && validatedSession.user.id === initialSession.user.id) {
            console.log('✅ Session validation successful for user:', validatedSession.user.email);
            session.set(validatedSession);
            sessionManager = new SessionManager();
          } else {
            console.log('❌ Session validation failed - user mismatch or invalid session');
            await authActions.forceSessionReset();
          }
        } catch (error) {
          console.error('❌ Session validation failed:', error);
          await authActions.forceSessionReset();
        }
      } else {
        console.log('✅ No initial session found - starting fresh');
      }
      
      console.log('✅ Auth initialization completed');
    } catch (error) {
      console.error('❌ Auth initialization error:', error);
      session.set(null);
      userProfile.set(null);
    }
  };

  // Listen to auth state changes with strict control
  nhost.auth.onAuthStateChanged((event: string, nhostSession: any) => {
    // CRITICAL: Ignore auth state changes during logout process
    if (isSigningOut) {
      console.log('IGNORING auth state change during logout:', event);
      return;
    }
    
    // CRITICAL: Ignore if auth listener is disabled
    if (!authStateListenerActive) {
      console.log('IGNORING auth state change - listener disabled:', event);
      return;
    }
    
    console.log('Auth state changed:', event, 'User ID:', nhostSession?.user?.id, 'Email:', nhostSession?.user?.email);
    
    if (event === 'SIGNED_IN' && nhostSession?.user) {
      // Only set session for valid sign-in events
      session.set(nhostSession);
      // Start session management
      if (!sessionManager) {
        sessionManager = new SessionManager();
      }
      console.log('Session established for user:', nhostSession.user.email);
    } else if (event === 'SIGNED_OUT' || event === 'TOKEN_CHANGED' || !nhostSession?.user) {
      // CRITICAL: Only clear if not already in logout process
      if (!isSigningOut) {
        session.set(null);
        userProfile.set(null);
        sessionWarning.set(false);
        sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
        
        if (sessionManager) {
          sessionManager.destroy();
          sessionManager = null;
        }
        
        console.log('Session cleared due to:', event);
      }
    }
  });

  initializeAuth();
}

// Enhanced auth actions
export const authActions = {
  signUp: async (email: string, password: string, options: SignUpOptions = {}): Promise<AuthResult> => {
    const result = await nhost.auth.signUp({
      email,
      password,
      ...options
    });
    return result as AuthResult;
  },

  signIn: async (email: string, password: string): Promise<AuthResult> => {
    // Store device info for tracking (only in browser)
    let deviceInfo = {};
    if (browser && typeof navigator !== 'undefined') {
      deviceInfo = {
        userAgent: navigator.userAgent,
        platform: navigator.platform,
        language: navigator.language,
        timestamp: new Date().toISOString()
      };
    }

    const result = await nhost.auth.signIn({
      email,
      password
    });

    if (result.session?.user && browser) {
      // Track login
      console.log('User signed in successfully');
    }

    return result as AuthResult;
  },

  // Disable auth state listener (nuclear option)
  disableAuthListener: () => {
    console.log('🚫 DISABLING AUTH LISTENER');
    authStateListenerActive = false;
    isSigningOut = true;
  },

  // Re-enable auth state listener 
  enableAuthListener: () => {
    console.log('✅ ENABLING AUTH LISTENER');
    authStateListenerActive = true;
    isSigningOut = false;
  },

  signOut: async (): Promise<AuthResult> => {
    console.log('🚨 STARTING SIGN OUT PROCESS 🚨');
    
    // CRITICAL: Set logout flag to ignore auth state changes
    isSigningOut = true;
    authStateListenerActive = false;
    
    // IMMEDIATELY clear our stores FIRST
    session.set(null);
    userProfile.set(null);
    sessionWarning.set(false);
    sessionTimeRemaining.set(SESSION_CONFIG.IDLE_TIMEOUT);
    
    // Clean up session manager immediately
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    console.log('✅ Internal state cleared immediately');
    
    // CRITICAL SECURITY FIX: Comprehensive storage clearing BEFORE nHost signOut
    if (browser) {
      try {
        console.log('🧹 Clearing all browser storage...');
        
        // Clear ALL localStorage data
        localStorage.clear();
        
        // Clear ALL sessionStorage data  
        sessionStorage.clear();
        
        // Clear ALL cookies for current domain
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
        });
        
        // Clear IndexedDB (where nHost stores session data)
        if ('indexedDB' in window) {
          const databases = await indexedDB.databases();
          console.log('🗂️ Found IndexedDB databases:', databases.map(db => db.name));
          
          await Promise.all(
            databases.map(db => {
              console.log('🗑️ Deleting database:', db.name);
              return new Promise<void>((resolve) => {
                const deleteReq = indexedDB.deleteDatabase(db.name!);
                deleteReq.onsuccess = () => {
                  console.log('✅ Successfully deleted database:', db.name);
                  resolve();
                };
                deleteReq.onerror = () => {
                  console.log('❌ Error deleting database:', db.name);
                  resolve(); // Don't fail if can't delete
                };
              });
            })
          );
        }
        
        console.log('✅ Browser storage cleared successfully');
      } catch (error) {
        console.error('❌ Storage clearing error:', error);
        // Continue with logout even if storage clearing fails
      }
    }
    
    // Call nHost signOut (but ignore the response since we've already cleared everything)
    let result: any = { error: null };
    try {
      console.log('🔌 Calling nHost signOut...');
      result = await nhost.auth.signOut();
      console.log('📡 nHost signOut result:', result);
    } catch (error) {
      console.error('❌ nHost signOut error:', error);
      // Don't fail - we've already cleared everything locally
    }
    
    if (browser) {
      // Final storage clear after nHost signOut
      try {
        localStorage.clear();
        sessionStorage.clear();
        console.log('🧽 Final storage clear completed');
      } catch (error) {
        console.error('❌ Final storage clear error:', error);
      }
      
      // Force redirect to login with cache busting
      console.log('🔄 Redirecting to login page...');
      const timestamp = Date.now();
      window.location.href = `/?_t=${timestamp}`;
    }
    
    console.log('✅ SIGN OUT PROCESS COMPLETED');
    return result as AuthResult;
  },

  // Extend current session (for warning dialog)
  extendSession: () => {
    if (sessionManager) {
      sessionManager.extendSession();
    }
  },

  // Force session termination
  forceLogout: async () => {
    session.set(null);
    userProfile.set(null);
    sessionWarning.set(false);
    
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    if (browser) {
      try {
        // Clear ALL browser storage
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear cookies
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
        });
        
        // Clear IndexedDB
        if ('indexedDB' in window) {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => {
              if (db.name?.includes('nhost') || db.name?.includes('auth') || db.name?.includes('hasura')) {
                return new Promise<void>((resolve) => {
                  const deleteReq = indexedDB.deleteDatabase(db.name!);
                  deleteReq.onsuccess = () => resolve();
                  deleteReq.onerror = () => resolve();
                });
              }
              return Promise.resolve();
            })
          );
        }
      } catch (error) {
        console.error('Force logout storage clearing error:', error);
      }
      
      window.location.href = '/';
    }
  },

  // NUCLEAR OPTION: Complete session reset
  forceSessionReset: async () => {
    console.log('FORCE SESSION RESET - Nuclear option activated');
    
    if (browser) {
      try {
        // Clear everything aggressively
        localStorage.clear();
        sessionStorage.clear();
        
        // Clear all cookies
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/`;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=${window.location.hostname}`;
          document.cookie = `${name.trim()}=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/;domain=.${window.location.hostname}`;
        });
        
        // Nuclear option: Clear ALL IndexedDB databases
        if ('indexedDB' in window) {
          const databases = await indexedDB.databases();
          await Promise.all(
            databases.map(db => new Promise<void>((resolve) => {
              console.log('NUCLEAR: Deleting database:', db.name);
              const deleteReq = indexedDB.deleteDatabase(db.name!);
              deleteReq.onsuccess = () => resolve();
              deleteReq.onerror = () => resolve();
            }))
          );
        }
      } catch (error) {
        console.error('Force reset error:', error);
      }
    }
    
    // Clear all internal state
    session.set(null);
    userProfile.set(null);
    sessionWarning.set(false);
    
    if (sessionManager) {
      sessionManager.destroy();
      sessionManager = null;
    }
    
    // Multiple nHost signOut calls
    try {
      await nhost.auth.signOut();
      await nhost.auth.signOut(); // Double signOut
      
      // NUCLEAR: Recreate the entire nHost client
      recreateNhostClient();
    } catch (error) {
      console.error('Force nHost signOut error:', error);
    }
    
    if (browser) {
      // Force page reload to completely reset everything
      window.location.href = '/?force_reset=true';
    }
  },

  sendPasswordResetEmail: async (email: string): Promise<AuthResult> => {
    const result = await nhost.auth.resetPassword({ email });
    return result as AuthResult;
  },

  resetPassword: async (password: string, passwordResetToken: string): Promise<AuthResult> => {
    const result = await nhost.auth.changePassword({
      newPassword: password,
      ticket: passwordResetToken
    });
    return result as AuthResult;
  }
};

// Export session configuration for components
export { SESSION_CONFIG };
