import { HoudiniClient } from '$houdini';
import { browser } from '$app/environment';
import { nhost } from './nhost.js';

// Create Houdini client with auth
export const houdiniClient = new HoudiniClient({
  url: 'https://pttthnqikxdsxmeccqho.graphql.us-west-2.nhost.run/v1',
  fetchParams() {
    // Get the current session token
    const session = browser ? nhost.auth.getSession() : null;
    
    return {
      headers: {
        ...(session?.accessToken && {
          Authorization: `Bearer ${session.accessToken}`
        })
      }
    };
  }
});
