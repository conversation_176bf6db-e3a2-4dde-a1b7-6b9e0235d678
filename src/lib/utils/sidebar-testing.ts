/**
 * Comprehensive testing utilities for sidebar functionality
 * Includes accessibility, performance, and interaction testing
 */

import { expect } from '@playwright/test';
import type { Page, Locator } from '@playwright/test';

export class SidebarTestHelper {
  private page: Page;
  private sidebarRoot: Locator;
  private toggleButton: Locator;

  constructor(page: Page) {
    this.page = page;
    this.sidebarRoot = page.locator('[data-slot="sidebar-root"]');
    this.toggleButton = page.locator('[data-sidebar="trigger"]');
  }

  /**
   * Test basic sidebar functionality
   */
  async testBasicFunctionality() {
    // Test initial state
    await expect(this.sidebarRoot).toBeVisible();
    
    // Test toggle functionality
    await this.toggleButton.click();
    await this.page.waitForTimeout(350); // Wait for animation
    
    // Test keyboard shortcut (Ctrl/Cmd + B)
    await this.page.keyboard.press('Meta+b');
    await this.page.waitForTimeout(350);
  }

  /**
   * Test accessibility compliance
   */
  async testAccessibility() {
    // Check ARIA attributes
    await expect(this.sidebarRoot).toHaveAttribute('role', 'navigation');
    await expect(this.sidebarRoot).toHaveAttribute('aria-label', 'Main navigation');
    
    // Test keyboard navigation
    const menuItems = this.page.locator('[data-sidebar="menu-button"]');
    const firstItem = menuItems.first();
    const lastItem = menuItems.last();
    
    await firstItem.focus();
    await this.page.keyboard.press('ArrowDown');
    await this.page.keyboard.press('ArrowUp');
    await this.page.keyboard.press('Home');
    await this.page.keyboard.press('End');
    
    // Test escape key on mobile
    await this.page.setViewportSize({ width: 500, height: 800 });
    await this.page.keyboard.press('Escape');
    await this.page.setViewportSize({ width: 1200, height: 800 });
  }

  /**
   * Test responsive behavior
   */
  async testResponsiveBehavior() {
    // Test desktop view
    await this.page.setViewportSize({ width: 1200, height: 800 });
    await expect(this.sidebarRoot).toBeVisible();
    
    // Test tablet view
    await this.page.setViewportSize({ width: 768, height: 800 });
    await this.page.waitForTimeout(100);
    
    // Test mobile view
    await this.page.setViewportSize({ width: 400, height: 800 });
    await this.page.waitForTimeout(100);
    
    // Reset to desktop
    await this.page.setViewportSize({ width: 1200, height: 800 });
  }

  /**
   * Test animation performance
   */
  async testAnimationPerformance() {
    // Start performance monitoring
    const performanceMetrics = await this.page.evaluate(() => {
      return new Promise((resolve) => {
        let frameCount = 0;
        const startTime = performance.now();
        
        const measureFrame = () => {
          frameCount++;
          if (frameCount < 60) { // Monitor for ~1 second at 60fps
            requestAnimationFrame(measureFrame);
          } else {
            const endTime = performance.now();
            const duration = (endTime - startTime) / 1000;
            const fps = frameCount / duration;
            resolve({ fps: Math.round(fps), duration });
          }
        };
        
        requestAnimationFrame(measureFrame);
      });
    });
    
    // Trigger sidebar toggle during monitoring
    await this.toggleButton.click();
    await this.page.waitForTimeout(350);
    
    return performanceMetrics;
  }

  /**
   * Test touch gestures on mobile
   */
  async testTouchGestures() {
    await this.page.setViewportSize({ width: 400, height: 800 });
    
    // Test swipe to open
    await this.page.touchscreen.tap(50, 400);
    await this.page.touchscreen.tap(200, 400);
    await this.page.waitForTimeout(350);
    
    // Test swipe to close
    await this.page.touchscreen.tap(200, 400);
    await this.page.touchscreen.tap(50, 400);
    await this.page.waitForTimeout(350);
    
    await this.page.setViewportSize({ width: 1200, height: 800 });
  }

  /**
   * Test menu item interactions
   */
  async testMenuItemInteractions() {
    const menuItems = this.page.locator('[data-sidebar="menu-button"]');
    const firstItem = menuItems.first();
    
    // Test hover state
    await firstItem.hover();
    await this.page.waitForTimeout(100);
    
    // Test active state
    await firstItem.click();
    await this.page.waitForTimeout(350);
    
    // Check aria-current attribute
    await expect(firstItem).toHaveAttribute('aria-current', 'page');
  }

  /**
   * Test state persistence
   */
  async testStatePersistence() {
    // Toggle sidebar
    await this.toggleButton.click();
    await this.page.waitForTimeout(350);
    
    // Reload page
    await this.page.reload();
    await this.page.waitForLoadState('networkidle');
    
    // Check if state is preserved (implementation dependent)
    // This would need to be customized based on your cookie/localStorage implementation
  }

  /**
   * Test error handling
   */
  async testErrorHandling() {
    // Test navigation with invalid URL
    await this.page.evaluate(() => {
      const button = document.querySelector('[data-sidebar="menu-button"]') as HTMLButtonElement;
      if (button) {
        // Simulate navigation error
        button.onclick = () => {
          throw new Error('Navigation error');
        };
        button.click();
      }
    });
    
    await this.page.waitForTimeout(100);
    // Verify error is handled gracefully
  }

  /**
   * Comprehensive test suite
   */
  async runFullTestSuite() {
    const results = {
      basicFunctionality: false,
      accessibility: false,
      responsiveBehavior: false,
      animationPerformance: null as any,
      touchGestures: false,
      menuInteractions: false,
      statePersistence: false,
      errorHandling: false
    };

    try {
      await this.testBasicFunctionality();
      results.basicFunctionality = true;
    } catch (e) {
      console.error('Basic functionality test failed:', e);
    }

    try {
      await this.testAccessibility();
      results.accessibility = true;
    } catch (e) {
      console.error('Accessibility test failed:', e);
    }

    try {
      await this.testResponsiveBehavior();
      results.responsiveBehavior = true;
    } catch (e) {
      console.error('Responsive behavior test failed:', e);
    }

    try {
      results.animationPerformance = await this.testAnimationPerformance();
    } catch (e) {
      console.error('Animation performance test failed:', e);
    }

    try {
      await this.testTouchGestures();
      results.touchGestures = true;
    } catch (e) {
      console.error('Touch gestures test failed:', e);
    }

    try {
      await this.testMenuItemInteractions();
      results.menuInteractions = true;
    } catch (e) {
      console.error('Menu interactions test failed:', e);
    }

    try {
      await this.testStatePersistence();
      results.statePersistence = true;
    } catch (e) {
      console.error('State persistence test failed:', e);
    }

    try {
      await this.testErrorHandling();
      results.errorHandling = true;
    } catch (e) {
      console.error('Error handling test failed:', e);
    }

    return results;
  }
}

/**
 * Visual regression test helper
 */
export async function captureVisualStates(page: Page) {
  const states = {
    expanded: null as Buffer | null,
    collapsed: null as Buffer | null,
    mobile: null as Buffer | null
  };

  // Capture expanded state
  await page.setViewportSize({ width: 1200, height: 800 });
  states.expanded = await page.screenshot({ 
    clip: { x: 0, y: 0, width: 300, height: 800 }
  });

  // Capture collapsed state
  const toggleButton = page.locator('[data-sidebar="trigger"]');
  await toggleButton.click();
  await page.waitForTimeout(350);
  states.collapsed = await page.screenshot({ 
    clip: { x: 0, y: 0, width: 100, height: 800 }
  });

  // Capture mobile state
  await page.setViewportSize({ width: 400, height: 800 });
  states.mobile = await page.screenshot({ 
    clip: { x: 0, y: 0, width: 400, height: 800 }
  });

  return states;
}

/**
 * Performance benchmark helper
 */
export async function benchmarkSidebarPerformance(page: Page, iterations: number = 10) {
  const toggleButton = page.locator('[data-sidebar="trigger"]');
  const results = [];

  for (let i = 0; i < iterations; i++) {
    const startTime = await page.evaluate(() => performance.now());
    await toggleButton.click();
    await page.waitForTimeout(350);
    const endTime = await page.evaluate(() => performance.now());
    
    results.push(endTime - startTime);
  }

  return {
    average: results.reduce((a, b) => a + b, 0) / results.length,
    min: Math.min(...results),
    max: Math.max(...results),
    results
  };
}
