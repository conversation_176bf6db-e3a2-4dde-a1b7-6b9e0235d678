import { nhost } from '$lib/stores/nhost.js';
import { browser } from '$app/environment';

export type AccountStatus = 'not_exists' | 'unverified' | 'verified' | 'unknown';

export interface AccountStatusResult {
  status: AccountStatus;
  email: string;
  canResend: boolean;
  attemptsUsed: number;
  maxAttempts: number;
}

/**
 * Check if an email account exists and its verification status
 * Uses nHost signUp pattern to detect account state
 */
export async function checkAccountStatus(email: string): Promise<AccountStatusResult> {
  const defaultResult: AccountStatusResult = {
    status: 'unknown',
    email,
    canResend: false,
    attemptsUsed: 0,
    maxAttempts: 3
  };

  try {
    // Check rate limit first
    const rateLimitInfo = checkResendRateLimit();
    
    // Try signUp with allowSignUp: false to check account status
    const result = await nhost.auth.signUp({
      email,
      password: '', // Empty password for status check
      options: {
        allowSignUp: false // This should trigger appropriate errors
      }
    });

    // Analyze the response to determine account status
    if (result.error) {
      const errorMessage = result.error.message?.toLowerCase() || '';
      
      if (errorMessage.includes('user already registered') || 
          errorMessage.includes('already exists') ||
          errorMessage.includes('email already in use')) {
        // Account exists, but we need to determine if it's verified
        // For now, we'll assume unverified since that's the common case
        return {
          status: 'unverified',
          email,
          canResend: rateLimitInfo.canResend,
          attemptsUsed: rateLimitInfo.attemptsUsed,
          maxAttempts: rateLimitInfo.maxAttempts
        };
      } else if (errorMessage.includes('not found') || 
                 errorMessage.includes('does not exist')) {
        return {
          status: 'not_exists',
          email,
          canResend: rateLimitInfo.canResend,
          attemptsUsed: rateLimitInfo.attemptsUsed,
          maxAttempts: rateLimitInfo.maxAttempts
        };
      }
    } else if (result.session) {
      // Successfully created session means account was created or logged in
      // This shouldn't happen with allowSignUp: false, but handle it
      return {
        status: 'verified',
        email,
        canResend: false,
        attemptsUsed: rateLimitInfo.attemptsUsed,
        maxAttempts: rateLimitInfo.maxAttempts
      };
    }

    return defaultResult;
  } catch (error) {
    console.error('Error checking account status:', error);
    return defaultResult;
  }
}

/**
 * Send verification email for existing unverified account
 * Extracted from resend-verification page logic
 */
export async function resendVerificationEmail(email: string): Promise<{
  success: boolean;
  message: string;
  attemptsUsed: number;
  maxAttempts: number;
}> {
  // Check rate limit
  const rateLimitInfo = checkResendRateLimit();
  
  if (!rateLimitInfo.canResend) {
    return {
      success: false,
      message: `You've reached the daily limit of ${rateLimitInfo.maxAttempts} verification attempts. Please contact support if you need assistance.`,
      attemptsUsed: rateLimitInfo.attemptsUsed,
      maxAttempts: rateLimitInfo.maxAttempts
    };
  }

  try {
    // Use the same pattern as resend verification page
    const result = await nhost.auth.signUp({
      email,
      password: '', // Empty password for resend
      options: {
        allowSignUp: false
      }
    });

    // Increment attempt count regardless of result
    incrementResendAttemptCount();
    const newRateLimitInfo = checkResendRateLimit();

    if (result.error) {
      // "User already registered" is expected and means resend was triggered
      if (result.error.message?.includes('User already registered')) {
        return {
          success: true,
          message: 'Verification email sent! Please check your inbox and spam folder.',
          attemptsUsed: newRateLimitInfo.attemptsUsed,
          maxAttempts: newRateLimitInfo.maxAttempts
        };
      } else {
        return {
          success: false,
          message: result.error.message || 'Failed to send verification email',
          attemptsUsed: newRateLimitInfo.attemptsUsed,
          maxAttempts: newRateLimitInfo.maxAttempts
        };
      }
    } else {
      return {
        success: true,
        message: 'Verification email sent! Please check your inbox and spam folder.',
        attemptsUsed: newRateLimitInfo.attemptsUsed,
        maxAttempts: newRateLimitInfo.maxAttempts
      };
    }
  } catch (error) {
    console.error('Resend verification error:', error);
    return {
      success: false,
      message: 'An unexpected error occurred. Please try again.',
      attemptsUsed: rateLimitInfo.attemptsUsed,
      maxAttempts: rateLimitInfo.maxAttempts
    };
  }
}

/**
 * Rate limiting functions extracted from resend verification page
 */
export interface RateLimitInfo {
  canResend: boolean;
  attemptsUsed: number;
  maxAttempts: number;
  rateLimitKey: string;
}

export function checkResendRateLimit(): RateLimitInfo {
  const maxAttempts = 3;
  
  if (!browser) {
    return {
      canResend: true,
      attemptsUsed: 0,
      maxAttempts,
      rateLimitKey: ''
    };
  }
  
  const today = new Date().toDateString();
  const rateLimitKey = `verification_attempts_${today}`;
  const stored = localStorage.getItem(rateLimitKey);
  const attemptsUsed = stored ? parseInt(stored) : 0;
  
  return {
    canResend: attemptsUsed < maxAttempts,
    attemptsUsed,
    maxAttempts,
    rateLimitKey
  };
}

export function incrementResendAttemptCount(): void {
  if (!browser) return;
  
  const rateLimitInfo = checkResendRateLimit();
  const newCount = rateLimitInfo.attemptsUsed + 1;
  localStorage.setItem(rateLimitInfo.rateLimitKey, newCount.toString());
}
