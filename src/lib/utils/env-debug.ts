/**
 * Utility for debugging environment variable access
 * Call this from your application to verify environment variables
 */

import { browser } from '$app/environment';

export function debugEnvironmentVariables() {
  // Only run on client-side
  if (!browser) return {};
  
  const envVars = {
    PUBLIC_NHOST_SUBDOMAIN: import.meta.env.PUBLIC_NHOST_SUBDOMAIN,
    PUBLIC_NHOST_REGION: import.meta.env.PUBLIC_NHOST_REGION,
    PUBLIC_GRAPHQL_ENDPOINT: import.meta.env.PUBLIC_GRAPHQL_ENDPOINT,
    PUBLIC_APP_NAME: import.meta.env.PUBLIC_APP_NAME,
    PUBLIC_APP_URL: import.meta.env.PUBLIC_APP_URL,
    NODE_ENV: import.meta.env.NODE_ENV,
    MODE: import.meta.env.MODE,
    BASE_URL: import.meta.env.BASE_URL,
    // Check for alternate prefixes
    VITE_NHOST_SUBDOMAIN: import.meta.env.VITE_NHOST_SUBDOMAIN,
    VITE_NHOST_REGION: import.meta.env.VITE_NHOST_REGION,
    VITE_GRAPHQL_ENDPOINT: import.meta.env.VITE_GRAPHQL_ENDPOINT,
  };
  
  console.log('Environment Variables:', envVars);
  return envVars;
}

// Auto-invoke in development mode for easy debugging
if (import.meta.env.DEV) {
  debugEnvironmentVariables();
}