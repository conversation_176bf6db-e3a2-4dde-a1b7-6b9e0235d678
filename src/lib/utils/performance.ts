/**
 * Performance monitoring utilities for sidebar animations
 * Helps track animation performance and memory usage
 */

interface PerformanceMetrics {
  animationFrameRate: number;
  memoryUsage?: number;
  renderTime: number;
  isGPUAccelerated: boolean;
}

class SidebarPerformanceMonitor {
  private frameCount = 0;
  private startTime = 0;
  private animationId: number | null = null;
  private isMonitoring = false;

  /**
   * Start monitoring sidebar animation performance
   */
  startMonitoring(): void {
    if (this.isMonitoring) return;
    
    this.isMonitoring = true;
    this.frameCount = 0;
    this.startTime = performance.now();
    
    const measureFrame = () => {
      this.frameCount++;
      
      if (this.isMonitoring) {
        this.animationId = requestAnimationFrame(measureFrame);
      }
    };
    
    this.animationId = requestAnimationFrame(measureFrame);
  }

  /**
   * Stop monitoring and return performance metrics
   */
  stopMonitoring(): PerformanceMetrics {
    this.isMonitoring = false;
    
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    
    const endTime = performance.now();
    const duration = (endTime - this.startTime) / 1000; // Convert to seconds
    const frameRate = duration > 0 ? this.frameCount / duration : 0;
    
    return {
      animationFrameRate: Math.round(frameRate),
      memoryUsage: this.getMemoryUsage(),
      renderTime: endTime - this.startTime,
      isGPUAccelerated: this.checkGPUAcceleration()
    };
  }

  /**
   * Get memory usage if available
   */
  private getMemoryUsage(): number | undefined {
    // @ts-ignore - performance.memory is not standardized but available in Chrome
    return (performance as any).memory?.usedJSHeapSize;
  }

  /**
   * Check if GPU acceleration is being used
   */
  private checkGPUAcceleration(): boolean {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    return !!gl;
  }

  /**
   * Monitor a specific sidebar animation
   */
  async monitorSidebarToggle(sidebarElement: HTMLElement): Promise<PerformanceMetrics> {
    return new Promise((resolve) => {
      this.startMonitoring();
      
      // Listen for transition end
      const handleTransitionEnd = () => {
        sidebarElement.removeEventListener('transitionend', handleTransitionEnd);
        const metrics = this.stopMonitoring();
        resolve(metrics);
      };
      
      sidebarElement.addEventListener('transitionend', handleTransitionEnd);
      
      // Fallback timeout in case transition doesn't fire
      setTimeout(() => {
        sidebarElement.removeEventListener('transitionend', handleTransitionEnd);
        const metrics = this.stopMonitoring();
        resolve(metrics);
      }, 1000);
    });
  }
}

/**
 * Singleton instance for global access
 */
export const sidebarPerformanceMonitor = new SidebarPerformanceMonitor();

/**
 * Helper function to log performance metrics in development
 */
export function logSidebarPerformance(metrics: PerformanceMetrics): void {
  if (import.meta.env.DEV) {
    console.group('🚀 Sidebar Performance Metrics');
    console.log(`Frame Rate: ${metrics.animationFrameRate} FPS`);
    console.log(`Render Time: ${metrics.renderTime.toFixed(2)}ms`);
    console.log(`GPU Accelerated: ${metrics.isGPUAccelerated ? '✅' : '❌'}`);
    
    if (metrics.memoryUsage) {
      console.log(`Memory Usage: ${(metrics.memoryUsage / 1024 / 1024).toFixed(2)} MB`);
    }
    
    // Performance warnings
    if (metrics.animationFrameRate < 30) {
      console.warn('⚠️ Low frame rate detected. Consider optimizing animations.');
    }
    
    if (!metrics.isGPUAccelerated) {
      console.warn('⚠️ GPU acceleration not detected. Ensure transform properties are used.');
    }
    
    console.groupEnd();
  }
}

/**
 * Performance testing helper for development
 */
export async function testSidebarPerformance(): Promise<void> {
  if (!import.meta.env.DEV) return;
  
  const sidebarElement = document.querySelector('[data-slot="sidebar-root"]') as HTMLElement;
  if (!sidebarElement) return;
  
  console.log('🧪 Testing sidebar performance...');
  
  const metrics = await sidebarPerformanceMonitor.monitorSidebarToggle(sidebarElement);
  logSidebarPerformance(metrics);
}
