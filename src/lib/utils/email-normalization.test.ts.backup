/**
 * Test cases for email normalization utility
 * Run with: npm test or vitest
 */

import { describe, it, expect } from 'vitest';
import { 
  normalizeEmail, 
  normalizeGmailAddress, 
  hasEmailAliases,
  areEmailsEquivalent,
  validateEmailForRegistration,
  isGmailDomain,
  supportsPlusAliasing
} from './email-normalization';

describe('Email Normalization', () => {
  describe('Gmail Normalization', () => {
    it('should normalize Gmail addresses correctly', () => {
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
    });

    it('should handle Google domain variations', () => {
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
    });

    it('should handle case insensitive emails', () => {
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
    });
  });

  describe('Other Provider Normalization', () => {
    it('should only remove plus aliases for other providers', () => {
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
    });

    it('should preserve dots for non-Gmail providers', () => {
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
      expect(normalizeEmail('<EMAIL>')).toBe('<EMAIL>');
    });
  });

  describe('Domain Detection', () => {
    it('should correctly identify Gmail domains', () => {
      expect(isGmailDomain('gmail.com')).toBe(true);
      expect(isGmailDomain('googlemail.com')).toBe(true);
      expect(isGmailDomain('google.com')).toBe(true);
      expect(isGmailDomain('outlook.com')).toBe(false);
    });

    it('should correctly identify plus alias support', () => {
      expect(supportsPlusAliasing('gmail.com')).toBe(true);
      expect(supportsPlusAliasing('outlook.com')).toBe(true);
      expect(supportsPlusAliasing('yahoo.com')).toBe(true);
      expect(supportsPlusAliasing('example.com')).toBe(false);
    });
  });

  describe('Email Equivalence', () => {
    it('should detect equivalent Gmail addresses', () => {
      expect(areEmailsEquivalent('<EMAIL>', '<EMAIL>')).toBe(true);
      expect(areEmailsEquivalent('<EMAIL>', '<EMAIL>')).toBe(true);
      expect(areEmailsEquivalent('<EMAIL>', '<EMAIL>')).toBe(true);
    });

    it('should detect non-equivalent addresses', () => {
      expect(areEmailsEquivalent('<EMAIL>', '<EMAIL>')).toBe(false);
      expect(areEmailsEquivalent('<EMAIL>', '<EMAIL>')).toBe(false);
    });
  });

  describe('Alias Detection', () => {
    it('should detect email aliases correctly', () => {
      const result = hasEmailAliases('<EMAIL>');
      expect(result.hasDots).toBe(true);
      expect(result.hasPlus).toBe(true);
      expect(result.isGmail).toBe(true);
      expect(result.normalizedEmail).toBe('<EMAIL>');
    });
  });

  describe('Registration Validation', () => {
    it('should validate email for registration with warnings', () => {
      const result = validateEmailForRegistration('<EMAIL>');
      expect(result.normalizedEmail).toBe('<EMAIL>');
      expect(result.warnings).toContain('Gmail addresses with dots are normalized (dots are ignored)');
      expect(result.warnings).toContain('Plus aliases (+text) are removed from email addresses');
    });

    it('should handle clean emails without warnings', () => {
      const result = validateEmailForRegistration('<EMAIL>');
      expect(result.normalizedEmail).toBe('<EMAIL>');
      expect(result.warnings).toHaveLength(0);
    });
  });

  describe('Error Handling', () => {
    it('should throw error for invalid emails', () => {
      expect(() => normalizeEmail('invalid-email')).toThrow('Invalid email address format');
      expect(() => normalizeEmail('')).toThrow('Invalid email address format');
      expect(() => normalizeEmail('user@')).toThrow('Invalid email address format');
    });
  });

  describe('Real-world Test Cases', () => {
    it('should prevent common Gmail exploitation patterns', () => {
      // All these should normalize to the same address
      const variations = [
        '<EMAIL>',
        '<EMAIL>', 
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      const normalized = variations.map(normalizeEmail);
      const expected = '<EMAIL>';
      
      normalized.forEach(email => {
        expect(email).toBe(expected);
      });
    });
  });
});
