/**
 * 8-Point Grid System for Consistent Spacing
 * Following modern design system best practices
 */

export const SPACING = {
  // Base units following 8px grid
  xs: "0.25rem", // 4px - half unit for fine adjustments
  sm: "0.5rem",  // 8px - base unit
  md: "1rem",    // 16px - standard component spacing
  lg: "1.5rem",  // 24px - section spacing
  xl: "2rem",    // 32px - large section spacing
  "2xl": "3rem", // 48px - page-level spacing
  "3xl": "4rem"  // 64px - major layout spacing
} as const;

/**
 * Sidebar-specific spacing constants
 */
export const SIDEBAR_SPACING = {
  // Content padding
  contentPadding: SPACING.md,      // 16px
  contentPaddingCollapsed: SPACING.sm, // 8px
  
  // Item spacing
  itemGap: SPACING.xs,             // 4px between items
  sectionGap: SPACING.sm,          // 8px between sections
  
  // Header/footer padding
  headerPadding: SPACING.md,       // 16px
  footerPadding: SPACING.md,       // 16px
  
  // Icon sizing and spacing
  iconSize: "1.25rem",             // 20px - optimal for readability
  iconGap: SPACING.md,             // 16px gap from text
  
  // Toggle button dimensions
  toggleSize: "2.25rem",           // 36px - proper touch target
  toggleSizeCollapsed: "2.5rem"    // 40px - larger for collapsed state
} as const;

/**
 * Animation constants for consistent motion
 */
export const ANIMATION = {
  // Durations
  fast: "150ms",
  normal: "300ms", 
  slow: "500ms",
  
  // Easing functions
  easeOut: "cubic-bezier(0.4, 0, 0.2, 1)",
  easeIn: "cubic-bezier(0.4, 0, 1, 1)",
  easeInOut: "cubic-bezier(0.4, 0, 0.2, 1)",
  
  // Sidebar-specific animations
  sidebarToggle: "300ms cubic-bezier(0.4, 0, 0.2, 1)",
  menuItemHover: "200ms cubic-bezier(0.4, 0, 0.2, 1)"
} as const;

/**
 * Helper function to get consistent spacing values
 */
export function getSpacing(size: keyof typeof SPACING): string {
  return SPACING[size];
}

/**
 * Helper function to create transition strings
 */
export function createTransition(
  properties: string[], 
  duration: keyof typeof ANIMATION = 'normal',
  easing: keyof typeof ANIMATION = 'easeOut'
): string {
  const durationValue = ANIMATION[duration];
  const easingValue = ANIMATION[easing];
  
  return properties
    .map(prop => `${prop} ${durationValue} ${easingValue}`)
    .join(', ');
}
