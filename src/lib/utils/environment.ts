/**
 * Environment Detection and Configuration Utility
 * 
 * Provides runtime environment detection and configuration management
 * for different deployment contexts (development, staging, production).
 * 
 * Enterprise-grade environment management with security best practices.
 */

import { browser } from '$app/environment';

/**
 * Environment types supported by SourceFlex
 */
export type Environment = 'development' | 'staging' | 'production';

/**
 * Environment configuration interface
 */
interface EnvironmentConfig {
  environment: Environment;
  isDevelopment: boolean;
  isStaging: boolean;
  isProduction: boolean;
  domain: string;
  turnstile: {
    siteKey: string;
    secretKey?: string; // Only available server-side
  };
  nhost: {
    subdomain: string;
    region: string;
    adminSecret?: string; // Only available server-side
  };
  features: {
    debugMode: boolean;
    verboseLogging: boolean;
    mockServices: boolean;
  };
}

/**
 * Detect current environment based on domain and environment variables
 */
export function detectEnvironment(): Environment {
  // Server-side detection using globals that are safe
  if (!browser) {
    // Check process.env first (Cloudflare Workers 2025+)
    if (typeof process !== 'undefined' && process.env?.NODE_ENV) {
      const nodeEnv = process.env.NODE_ENV;
      if (nodeEnv === 'production') return 'production';
      if (nodeEnv === 'staging') return 'staging';
    }
    
    // Fallback to globalThis
    if (typeof globalThis !== 'undefined' && globalThis.process?.env?.NODE_ENV) {
      const nodeEnv = globalThis.process.env.NODE_ENV;
      if (nodeEnv === 'production') return 'production';
      if (nodeEnv === 'staging') return 'staging';
    }
    
    // For Cloudflare Workers, check if we have production indicators
    if (typeof globalThis !== 'undefined') {
      // Check if we're in a Cloudflare Worker environment with production config
      const publicKey = globalThis.process?.env?.PUBLIC_TURNSTILE_SITE_KEY;
      if (publicKey && publicKey.startsWith('0x4')) {
        return 'production';
      }
    }
    
    return 'development';
  }

  // Client-side detection based on hostname
  const hostname = window.location.hostname;
  
  if (hostname === 'sourceflex.io') {
    return 'production';
  }
  
  if (hostname.includes('staging') || hostname.includes('preview')) {
    return 'staging';
  }
  
  if (hostname === 'localhost' || hostname.startsWith('127.0.0.1')) {
    return 'development';
  }
  
  // Default for unknown domains (e.g., Vercel previews)
  return 'staging';
}

/**
 * Get environment variable with fallback - Updated for Cloudflare Workers 2025
 */
function getEnvVar(key: string, fallback: string = ''): string {
  if (!browser) {
    // Server-side: Use process.env first (Cloudflare Workers 2025+), then platform
    if (typeof process !== 'undefined' && process.env) {
      const value = process.env[key];
      if (value) return value;
    }
    
    // Fallback to globalThis approach for older compatibility
    if (typeof globalThis !== 'undefined' && globalThis.process?.env) {
      const value = globalThis.process.env[key];
      if (value) return value;
    }
    
    return fallback;
  }
  
  // Client-side: check import.meta.env (only PUBLIC_ variables available)
  return import.meta.env[key] || fallback;
}

/**
 * Get environment-specific configuration
 */
export function getEnvironmentConfig(): EnvironmentConfig {
  const environment = detectEnvironment();
  const isDevelopment = environment === 'development';
  const isStaging = environment === 'staging';
  const isProduction = environment === 'production';

  // Domain configuration
  const domainMap: Record<Environment, string> = {
    development: 'http://localhost:5173',
    staging: 'https://sourceflex.vercel.app',
    production: 'https://sourceflex.io'
  };

  // Turnstile configuration based on environment
  const turnstileConfig = {
    siteKey: (() => {
      // Always use production key if we have it, unless explicitly in development
      const publicKey = getEnvVar('PUBLIC_TURNSTILE_SITE_KEY', '0x4AAAAAABllltaE5hyR1BQ5');
      
      // Only use test key in true development (localhost)
      if (isDevelopment && (!browser && !publicKey.startsWith('0x4'))) {
        return '1x00000000000000000000AA'; // Test key for development
      }
      
      // Use the production key for staging and production
      return publicKey;
    })(),
    secretKey: !browser ? getEnvVar('TURNSTILE_SECRET_KEY') : undefined
  };

  // nHost configuration
  const nhostConfig = {
    subdomain: getEnvVar('PUBLIC_NHOST_SUBDOMAIN', 'pttthnqikxdsxmeccqho'),
    region: getEnvVar('PUBLIC_NHOST_REGION', 'us-west-2'),
    adminSecret: !browser ? getEnvVar('NHOST_ADMIN_SECRET') : undefined
  };

  // Feature flags based on environment
  const features = {
    debugMode: isDevelopment || isStaging,
    verboseLogging: isDevelopment,
    mockServices: false // Can be overridden for testing
  };

  return {
    environment,
    isDevelopment,
    isStaging,
    isProduction,
    domain: domainMap[environment],
    turnstile: turnstileConfig,
    nhost: nhostConfig,
    features
  };
}

/**
 * Environment-aware console logging
 */
export function envLog(level: 'info' | 'warn' | 'error', message: string, data?: unknown): void {
  const config = getEnvironmentConfig();
  
  if (!config.features.verboseLogging && level === 'info') {
    return; // Skip info logs in non-development environments
  }
  
  const prefix = `[${config.environment.toUpperCase()}]`;
  
  switch (level) {
    case 'info':
      console.info(prefix, message, data);
      break;
    case 'warn':
      console.warn(prefix, message, data);
      break;
    case 'error':
      console.error(prefix, message, data);
      break;
  }
}

/**
 * Validate environment configuration
 */
export function validateEnvironmentConfig(): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const config = getEnvironmentConfig();
  const errors: string[] = [];
  const warnings: string[] = [];

  // Required environment variables
  if (!config.turnstile.siteKey) {
    errors.push('Missing Turnstile site key');
  }

  if (!browser && !config.turnstile.secretKey && config.isProduction) {
    errors.push('Missing Turnstile secret key in production');
  }

  if (!config.nhost.subdomain) {
    errors.push('Missing nHost subdomain');
  }

  if (!browser && !config.nhost.adminSecret && config.isProduction) {
    warnings.push('Missing nHost admin secret - some features may not work');
  }

  // Environment-specific validations
  if (config.isProduction) {
    if (config.features.debugMode) {
      warnings.push('Debug mode enabled in production');
    }
    
    if (config.turnstile.siteKey.startsWith('1x0000')) {
      errors.push('Using test Turnstile keys in production');
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Initialize environment and log configuration status
 */
export function initializeEnvironment(): EnvironmentConfig {
  const config = getEnvironmentConfig();
  const validation = validateEnvironmentConfig();

  envLog('info', 'Environment initialized', {
    environment: config.environment,
    domain: config.domain,
    turnstileSiteKey: config.turnstile.siteKey.substring(0, 10) + '...', // Only show first 10 chars for security
    features: config.features
  });

  if (!validation.isValid) {
    validation.errors.forEach(error => envLog('error', error));
  }

  validation.warnings.forEach(warning => envLog('warn', warning));

  return config;
}

// Export commonly used configuration
export const ENV_CONFIG = getEnvironmentConfig();
export const IS_DEVELOPMENT = ENV_CONFIG.isDevelopment;
export const IS_STAGING = ENV_CONFIG.isStaging;
export const IS_PRODUCTION = ENV_CONFIG.isProduction;
