/**
 * Touch gesture utilities for mobile sidebar interactions
 * Provides swipe gestures for intuitive sidebar control
 */

interface TouchPoint {
  x: number;
  y: number;
  timestamp: number;
}

interface SwipeGestureOptions {
  threshold: number;        // Minimum distance for swipe
  velocityThreshold: number; // Minimum velocity for swipe
  maxTime: number;          // Maximum time for swipe gesture
  preventScroll: boolean;   // Prevent scroll during gesture
}

const DEFAULT_OPTIONS: SwipeGestureOptions = {
  threshold: 50,        // 50px minimum swipe distance
  velocityThreshold: 0.3, // 0.3px/ms minimum velocity
  maxTime: 300,         // 300ms maximum swipe time
  preventScroll: true
};

export class TouchGestureHandler {
  private startTouch: TouchPoint | null = null;
  private element: HTMLElement;
  private options: SwipeGestureOptions;
  private onSwipeLeft?: () => void;
  private onSwipeRight?: () => void;

  constructor(
    element: HTMLElement, 
    options: Partial<SwipeGestureOptions> = {}
  ) {
    this.element = element;
    this.options = { ...DEFAULT_OPTIONS, ...options };
    this.bindEvents();
  }

  /**
   * Set swipe gesture callbacks
   */
  setGestureHandlers(onSwipeLeft?: () => void, onSwipeRight?: () => void): void {
    this.onSwipeLeft = onSwipeLeft;
    this.onSwipeRight = onSwipeRight;
  }

  /**
   * Bind touch events to element
   */
  private bindEvents(): void {
    this.element.addEventListener('touchstart', this.handleTouchStart, { passive: false });
    this.element.addEventListener('touchmove', this.handleTouchMove, { passive: false });
    this.element.addEventListener('touchend', this.handleTouchEnd, { passive: false });
    this.element.addEventListener('touchcancel', this.handleTouchCancel);
  }

  /**
   * Remove event listeners
   */
  destroy(): void {
    this.element.removeEventListener('touchstart', this.handleTouchStart);
    this.element.removeEventListener('touchmove', this.handleTouchMove);
    this.element.removeEventListener('touchend', this.handleTouchEnd);
    this.element.removeEventListener('touchcancel', this.handleTouchCancel);
  }

  private handleTouchStart = (e: TouchEvent): void => {
    if (e.touches.length !== 1) return;
    
    // Ignore touches on interactive elements (buttons, links, etc.)
    const target = e.target as HTMLElement;
    if (target.closest('button, a, [role="button"], [tabindex]')) {
      return;
    }
    
    const touch = e.touches[0];
    this.startTouch = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };
  };

  private handleTouchMove = (e: TouchEvent): void => {
    if (!this.startTouch || e.touches.length !== 1) return;
    
    // Prevent scrolling if option is enabled
    if (this.options.preventScroll) {
      const touch = e.touches[0];
      const deltaX = Math.abs(touch.clientX - this.startTouch.x);
      const deltaY = Math.abs(touch.clientY - this.startTouch.y);
      
      // If horizontal movement is greater than vertical, prevent default
      if (deltaX > deltaY) {
        e.preventDefault();
      }
    }
  };

  private handleTouchEnd = (e: TouchEvent): void => {
    if (!this.startTouch || e.changedTouches.length !== 1) return;
    
    const touch = e.changedTouches[0];
    const endTouch: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    };
    
    const deltaX = endTouch.x - this.startTouch.x;
    const deltaY = endTouch.y - this.startTouch.y;
    const deltaTime = endTouch.timestamp - this.startTouch.timestamp;
    
    // Check if gesture meets criteria
    const distance = Math.abs(deltaX);
    const velocity = distance / deltaTime;
    
    const isHorizontalSwipe = Math.abs(deltaX) > Math.abs(deltaY);
    const meetsThreshold = distance >= this.options.threshold;
    const meetsVelocity = velocity >= this.options.velocityThreshold;
    const withinTimeLimit = deltaTime <= this.options.maxTime;
    
    if (isHorizontalSwipe && meetsThreshold && meetsVelocity && withinTimeLimit) {
      if (deltaX > 0 && this.onSwipeRight) {
        this.onSwipeRight();
      } else if (deltaX < 0 && this.onSwipeLeft) {
        this.onSwipeLeft();
      }
    }
    
    this.startTouch = null;
  };

  private handleTouchCancel = (): void => {
    this.startTouch = null;
  };
}

/**
 * Svelte action for easy integration with sidebar components
 */
export function swipeGesture(
  element: HTMLElement, 
  options: {
    onSwipeLeft?: () => void;
    onSwipeRight?: () => void;
    gestureOptions?: Partial<SwipeGestureOptions>;
  }
) {
  const handler = new TouchGestureHandler(element, options.gestureOptions);
  handler.setGestureHandlers(options.onSwipeLeft, options.onSwipeRight);
  
  return {
    update(newOptions: typeof options) {
      handler.setGestureHandlers(newOptions.onSwipeLeft, newOptions.onSwipeRight);
    },
    destroy() {
      handler.destroy();
    }
  };
}

/**
 * Helper to determine if device supports touch
 */
export function isTouchDevice(): boolean {
  return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
}
