import { NhostClient } from '@nhost/nhost-js';
import { 
	PUBLIC_NHOST_SUBDOMAIN, 
	PUBLIC_NHOST_REGION 
} from '$env/static/public';

// nHost client configuration with real environment variables
export const nhost = new NhostClient({
	subdomain: PUBLIC_NHOST_SUBDOMAIN,
	region: PUBLIC_NHOST_REGION
});

// GraphQL client for <PERSON><PERSON><PERSON> (configured automatically)
// <PERSON><PERSON><PERSON> will use the endpoint from houdini.config.js

// Export auth helpers
export const { auth, storage, graphql } = nhost;
