<script lang="ts">
  import { sessionWarning, sessionTimeRemaining, authActions } from '$lib/stores/auth';
  import { Button } from '$lib/components/ui/button';

  // Format time remaining as MM:SS
  function formatTime(milliseconds: number): string {
    const totalSeconds = Math.floor(milliseconds / 1000);
    const minutes = Math.floor(totalSeconds / 60);
    const seconds = totalSeconds % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  }

  function handleExtendSession() {
    authActions.extendSession();
  }

  function handleSignOut() {
    authActions.signOut();
  }
</script>

{#if $sessionWarning}
  <!-- Session Warning Modal -->
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
      <div class="flex items-center mb-4">
        <svg class="h-6 w-6 text-yellow-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
        <h3 class="text-lg font-semibold text-gray-900">Session Expiring Soon</h3>
      </div>
      
      <p class="text-gray-600 mb-4">
        Your session will expire in <strong>{formatTime($sessionTimeRemaining)}</strong> due to inactivity.
      </p>
      
      <p class="text-sm text-gray-500 mb-6">
        Would you like to extend your session and continue working?
      </p>
      
      <div class="flex space-x-3">
        <Button onclick={handleExtendSession} class="flex-1">
          Continue Session
        </Button>
        <Button variant="outline" onclick={handleSignOut} class="flex-1">
          Sign Out
        </Button>
      </div>
    </div>
  </div>
{/if}
