<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { isAuthenticated, userRole, userRoles, session } from '$lib/stores/auth';

  interface Props {
    allowedRoles?: string[];
    redirectTo?: string;
    children?: any;
  }

  let { 
    allowedRoles, 
    redirectTo,
    children 
  }: Props = $props();

  // Set defaults after destructuring to avoid override issues
  const effectiveAllowedRoles = allowedRoles || ['user'];
  const effectiveRedirectTo = redirectTo || '/';

  let isLoading = $state(true);
  let hasAccess = $state(false);
  let isInitialized = $state(false);

  onMount(() => {
    console.log('ProtectedRoute - Component mounted');
    // Give a short delay for stores to initialize
    setTimeout(() => {
      isInitialized = true;
      console.log('ProtectedRoute - Initialization complete');
    }, 100);
  });

  // Reactive statement to handle authentication state changes
  $effect(() => {
    if (!isInitialized) return;
    
    const currentSession = $session;
    const authenticated = $isAuthenticated;
    const currentRole = $userRole;
    const currentRoles = $userRoles;
    
    console.log('ProtectedRoute - Reactive check:', {
      hasSession: !!currentSession,
      authenticated,
      currentRole,
      currentRoles,
      allowedRoles: effectiveAllowedRoles
    });
    
    if (!authenticated) {
      console.log('ProtectedRoute - Not authenticated, redirecting to login');
      hasAccess = false;
      isLoading = false;
      
      // Redirect to login with return URL
      setTimeout(() => {
        const returnTo = encodeURIComponent($page.url.pathname);
        goto(`${effectiveRedirectTo}?return=${returnTo}`);
      }, 100);
      return;
    }

    // Check role-based access
    const hasRequiredRole = effectiveAllowedRoles.includes('*') || 
                           (currentRole && effectiveAllowedRoles.includes(currentRole)) ||
                           effectiveAllowedRoles.some(role => currentRoles.includes(role));
    
    if (hasRequiredRole) {
      console.log('ProtectedRoute - Access granted');
      hasAccess = true;
      isLoading = false;
    } else {
      console.log('ProtectedRoute - Access denied, redirecting to unauthorized');
      hasAccess = false;
      isLoading = false;
      setTimeout(() => {
        goto('/unauthorized');
      }, 100);
    }
  });
</script>

{#if isLoading}
  <div class="flex items-center justify-center min-h-screen">
    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
  </div>
{:else if hasAccess}
  {@render children?.()}
{:else}
  <div class="flex items-center justify-center min-h-screen">
    <div class="text-center">
      <h1 class="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
      <p class="text-gray-600">You don't have permission to access this page.</p>
    </div>
  </div>
{/if}
