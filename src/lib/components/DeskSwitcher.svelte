<script lang="ts">
  import { userProfile } from '$lib/stores/auth.js';

  interface Props {
    onDeskChange?: (desk: 'recruitment' | 'bench_sales') => void;
  }

  const { onDeskChange }: Props = $props();

  let currentDesk = $state<'recruitment' | 'bench_sales'>('recruitment');

  // Update local state when profile changes
  $effect(() => {
    if ($userProfile?.current_desk) {
      currentDesk = $userProfile.current_desk;
    }
  });

  async function toggleDesk() {
    const newDesk = currentDesk === 'recruitment' ? 'bench_sales' : 'recruitment';
    
    try {
      // Update local state immediately for UI responsiveness
      currentDesk = newDesk;
      
      // Call parent callback if provided
      onDeskChange?.(newDesk);
      
      // Note: The actual profile update should be handled by parent component
      // This component is UI-only for the toggle functionality
      
    } catch (error) {
      console.error('Error switching desk:', error);
      // Revert on error
      currentDesk = currentDesk === 'recruitment' ? 'bench_sales' : 'recruitment';
    }
  }
</script>

<div class="flex items-center space-x-3">
  <span class="text-sm font-medium text-gray-700">Current Desk:</span>
  
  <button
    onclick={toggleDesk}
    class="relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent bg-gray-200 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-blue-600 focus:ring-offset-2 {currentDesk === 'bench_sales' ? 'bg-blue-600' : 'bg-gray-200'}"
    role="switch"
    aria-checked={currentDesk === 'bench_sales'}
    aria-label="Switch desk"
  >
    <span
      class="pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out {currentDesk === 'bench_sales' ? 'translate-x-5' : 'translate-x-0'}"
    ></span>
  </button>
  
  <div class="flex flex-col sm:flex-row sm:space-x-2 space-y-1 sm:space-y-0">
    <span class="text-sm {currentDesk === 'recruitment' ? 'font-semibold text-blue-600' : 'text-gray-500'}">
      Recruitment
    </span>
    <span class="hidden sm:inline text-gray-400">|</span>
    <span class="text-sm {currentDesk === 'bench_sales' ? 'font-semibold text-blue-600' : 'text-gray-500'}">
      Bench Sales
    </span>
  </div>
</div>
