<script lang="ts">
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';
  import { session, authActions } from '$lib/stores/auth.js';
  import toast from 'svelte-5-french-toast';
  import * as Sidebar from '$lib/components/ui/sidebar/index.js';
  import * as DropdownMenu from '$lib/components/ui/dropdown-menu/index.js';
  import * as Tooltip from '$lib/components/ui/tooltip/index.js';
  import { swipeGesture, isTouchDevice } from '$lib/utils/touch-gestures.js';
  import HomeIcon from "lucide-svelte/icons/house";
  import BriefcaseIcon from "lucide-svelte/icons/briefcase";
  import UsersIcon from "lucide-svelte/icons/users";
  import BuildingIcon from "lucide-svelte/icons/building-2";
  import TargetIcon from "lucide-svelte/icons/target";
  import SettingsIcon from "lucide-svelte/icons/settings";
  import UserIcon from "lucide-svelte/icons/user";
  import CreditCardIcon from "lucide-svelte/icons/credit-card";
  import LogOutIcon from "lucide-svelte/icons/log-out";
  import PanelLeftIcon from "lucide-svelte/icons/panel-left";
  import PanelRightIcon from "lucide-svelte/icons/panel-right";

  // Navigation items
  const navigationItems = [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: HomeIcon,
      isActive: () => $page.url.pathname === '/dashboard'
    },
    {
      title: 'Jobs',
      url: '/jobs',
      icon: BriefcaseIcon,
      isActive: () => $page.url.pathname.startsWith('/jobs')
    },
    {
      title: 'Candidates',
      url: '/candidates',
      icon: UsersIcon,
      isActive: () => $page.url.pathname.startsWith('/candidates')
    },
    {
      title: 'Clients',
      url: '/clients',
      icon: BuildingIcon,
      isActive: () => $page.url.pathname.startsWith('/clients')
    },
    {
      title: 'Bench Sales',
      url: '/bench',
      icon: TargetIcon,
      isActive: () => $page.url.pathname.startsWith('/bench')
    }
  ];

  // Get user details
  function getUserInitials(email: string): string {
    if (!email) return 'VA';
    return 'VA'; // Keep as requested in the image
  }

  function getUserName(email: string): string {
    if (!email) return 'vasanthan';
    return 'vasanthan'; // Keep as shown in image
  }

  function getUserEmail(): string {
    return '<EMAIL>'; // As shown in image
  }

  async function handleSignOut() {
    try {
      await authActions.signOut();
      toast.success('Signed out successfully');
    } catch (error) {
      console.error('Sign out error:', error);
      toast.error('Failed to sign out');
    }
  }

  // Get sidebar context
  const sidebar = Sidebar.useSidebar();

  // Touch gesture handlers for mobile
  function handleSwipeLeft() {
    if (sidebar.isMobile && sidebar.openMobile) {
      sidebar.setOpenMobile(false);
    }
  }

  function handleSwipeRight() {
    if (sidebar.isMobile && !sidebar.openMobile) {
      sidebar.setOpenMobile(true);
    }
  }

  // Robust toggle function that ensures proper state management
  function handleToggleClick() {
    // For desktop, use the main toggle
    if (!sidebar.isMobile) {
      sidebar.toggle();
    } else {
      // For mobile, toggle the mobile state
      sidebar.setOpenMobile(!sidebar.openMobile);
    }
  }
</script>

<Sidebar.Root 
  collapsible="icon" 
  class="border-r shadow-sm bg-sidebar"
  role="navigation"
  aria-label="Main navigation"
>
  <!-- Header with Logo Only -->
  <Sidebar.Header class="border-b px-4 py-4 group-data-[collapsible=icon]:px-3">
    <div class="flex items-center gap-3 group-data-[collapsible=icon]:justify-center">
      <div class="flex h-10 w-10 shrink-0 items-center justify-center rounded-xl bg-blue-600 text-white shadow-sm transition-transform duration-200 hover:scale-105">
        <span class="text-sm font-bold">SF</span>
      </div>
      <span class="font-bold text-lg text-sidebar-foreground group-data-[collapsible=icon]:hidden transition-opacity duration-300">
        SourceFlex
      </span>
    </div>
  </Sidebar.Header>

  <!-- Navigation Content -->
  <Sidebar.Content class="px-3 py-4 group-data-[collapsible=icon]:px-2">
    <div 
      use:swipeGesture={{
        onSwipeLeft: handleSwipeLeft,
        onSwipeRight: handleSwipeRight,
        gestureOptions: {
          threshold: 60,
          preventScroll: false
        }
      }}
    >
    <Sidebar.Group>
      <Sidebar.GroupLabel class="sr-only">Main Navigation</Sidebar.GroupLabel>
      <Sidebar.GroupContent>
        <Sidebar.Menu class="space-y-1 group-data-[collapsible=icon]:space-y-2" role="menu" aria-label="Main navigation menu">
          {#each navigationItems as item (item.title)}
            <Sidebar.MenuItem role="none">
              {#if !sidebar.isMobile && sidebar.state === 'collapsed'}
                <!-- Collapsed Mode - Icons with Tooltips -->
                <Tooltip.Root>
                  <Tooltip.Trigger>
                    {#snippet child({ props })}
                      <button 
                        {...props}
                        onclick={async (e) => {
                          e.preventDefault();
                          try {
                            await goto(item.url);
                          } catch (error) {
                            console.error('Navigation error:', error);
                            window.location.href = item.url;
                          }
                        }}
                        class="flex h-11 w-11 items-center justify-center rounded-lg mx-auto transition-all duration-200 hover:bg-sidebar-accent hover:scale-105 {item.isActive() ? 'bg-blue-100 text-blue-700 shadow-sm border-l-[3px] border-blue-700' : 'text-sidebar-foreground'}"
                        data-active={item.isActive()}
                        aria-label={item.title}
                        aria-current={item.isActive() ? 'page' : undefined}
                        tabindex="0"
                      >
                        <svelte:component this={item.icon} class="h-5 w-5" />
                      </button>
                    {/snippet}
                  </Tooltip.Trigger>
                  <Tooltip.Content side="right" class="font-medium">
                    {item.title}
                  </Tooltip.Content>
                </Tooltip.Root>
              {:else}
                <!-- Expanded Mode - Full Items -->
                <button 
                  onclick={async (e) => {
                    e.preventDefault();
                    // Navigate without toggling sidebar
                    try {
                      await goto(item.url);
                      // Preserve current sidebar state
                      sidebar.toggle(sidebar.open);
                    } catch (error) {
                      console.error('Navigation error:', error);
                      // Fallback to traditional navigation if goto fails
                      window.location.href = item.url;
                    }
                  }}
                  class="flex items-center gap-3 w-full text-left h-11 px-3 justify-start hover:bg-sidebar-accent hover:scale-[1.01] hover:translate-x-1 transition-all duration-200 rounded-lg {item.isActive() ? 'bg-blue-100 text-blue-700 font-medium shadow-sm border-l-[3px] border-blue-700 translate-x-1' : ''}"
                  aria-current={item.isActive() ? 'page' : undefined}
                  tabindex="0"
                  data-active={item.isActive()}
                >
                  <svelte:component this={item.icon} class="h-5 w-5 shrink-0" />
                  <span class="font-medium">{item.title}</span>
                </button>
              {/if}
            </Sidebar.MenuItem>
          {/each}
        </Sidebar.Menu>
      </Sidebar.GroupContent>
    </Sidebar.Group>
    </div>
  </Sidebar.Content>

  <!-- Footer with User Profile and Toggle -->
  <Sidebar.Footer class="border-t p-3 group-data-[collapsible=icon]:p-2">
    <Sidebar.Menu>
      <Sidebar.MenuItem>
        <div class="flex items-center gap-3 w-full group-data-[collapsible=icon]:flex-col group-data-[collapsible=icon]:gap-2">
          <!-- User Profile Area -->
          <div class="flex-1 min-w-0 group-data-[collapsible=icon]:flex-none">
            {#if !sidebar.isMobile && sidebar.state === 'collapsed'}
              <!-- Collapsed Profile - Avatar Only -->
              <div class="flex flex-col items-center gap-2">
                <DropdownMenu.Root>
                  <DropdownMenu.Trigger>
                    {#snippet child({ props })}
                      <button 
                        {...props}
                        class="flex h-11 w-11 items-center justify-center rounded-full bg-blue-600 text-white text-sm font-bold hover:bg-blue-700 transition-colors shadow-md hover:shadow-lg"
                      >
                        {getUserInitials($session?.user?.email || '')}
                      </button>
                    {/snippet}
                  </DropdownMenu.Trigger>
                  <DropdownMenu.Content side="right" align="end" class="w-56">
                    <DropdownMenu.Label>
                      <div class="flex flex-col space-y-1">
                        <p class="text-sm font-medium">{getUserName($session?.user?.email || '')}</p>
                        <p class="text-xs text-muted-foreground">{getUserEmail()}</p>
                      </div>
                    </DropdownMenu.Label>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item onclick={async () => {
                      try {
                        await goto('/profile');
                      } catch (error) {
                        console.error('Navigation error:', error);
                        window.location.href = '/profile';
                      }
                    }}>
                      <UserIcon class="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={async () => {
                      try {
                        await goto('/billing');
                      } catch (error) {
                        console.error('Navigation error:', error);
                        window.location.href = '/billing';
                      }
                    }}>
                      <CreditCardIcon class="mr-2 h-4 w-4" />
                      <span>Billing</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={async () => {
                      try {
                        await goto('/settings');
                      } catch (error) {
                        console.error('Navigation error:', error);
                        window.location.href = '/settings';
                      }
                    }}>
                      <SettingsIcon class="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item onclick={handleSignOut} class="text-red-600 focus:text-red-600">
                      <LogOutIcon class="mr-2 h-4 w-4" />
                      <span>Sign Out</span>
                    </DropdownMenu.Item>
                  </DropdownMenu.Content>
                </DropdownMenu.Root>
                
                <!-- Expand Button for Collapsed State -->
                <button 
                  onclick={handleToggleClick} 
                  class="sidebar-toggle flex h-9 w-9 items-center justify-center rounded-lg hover:bg-sidebar-accent transition-all duration-200 border border-sidebar-border hover:border-sidebar-accent"
                  title="Expand sidebar"
                  aria-label="Expand sidebar"
                >
                  <PanelRightIcon class="h-4 w-4" />
                </button>
              </div>
            {:else}
              <!-- Expanded Profile -->
              <div class="flex items-center gap-3 w-full">
                <DropdownMenu.Root>
                  <DropdownMenu.Trigger>
                    {#snippet child({ props })}
                      <button 
                        {...props}
                        class="flex items-center gap-3 w-full hover:bg-sidebar-accent rounded-lg p-2 transition-colors"
                      >
                        <div class="flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-blue-600 text-white text-sm font-bold">
                          {getUserInitials($session?.user?.email || '')}
                        </div>
                        <div class="flex flex-1 flex-col items-start text-left min-w-0">
                          <span class="text-sm font-semibold text-sidebar-foreground truncate">
                            {getUserName($session?.user?.email || '')}
                          </span>
                          <span class="text-xs text-sidebar-foreground/70 truncate">
                            {getUserEmail()}
                          </span>
                        </div>
                      </button>
                    {/snippet}
                  </DropdownMenu.Trigger>
                  <DropdownMenu.Content side="top" align="end" class="w-56">
                    <DropdownMenu.Item onclick={async () => {
                      try {
                        await goto('/profile');
                      } catch (error) {
                        console.error('Navigation error:', error);
                        window.location.href = '/profile';
                      }
                    }}>
                      <UserIcon class="mr-2 h-4 w-4" />
                      <span>Profile</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={async () => {
                      try {
                        await goto('/billing');
                      } catch (error) {
                        console.error('Navigation error:', error);
                        window.location.href = '/billing';
                      }
                    }}>
                      <CreditCardIcon class="mr-2 h-4 w-4" />
                      <span>Billing</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Item onclick={async () => {
                      try {
                        await goto('/settings');
                      } catch (error) {
                        console.error('Navigation error:', error);
                        window.location.href = '/settings';
                      }
                    }}>
                      <SettingsIcon class="mr-2 h-4 w-4" />
                      <span>Settings</span>
                    </DropdownMenu.Item>
                    <DropdownMenu.Separator />
                    <DropdownMenu.Item onclick={handleSignOut} class="text-red-600 focus:text-red-600">
                      <LogOutIcon class="mr-2 h-4 w-4" />
                      <span>Sign Out</span>
                    </DropdownMenu.Item>
                  </DropdownMenu.Content>
                </DropdownMenu.Root>
                
                <!-- Collapse Button for Expanded State -->
                <button 
                  onclick={handleToggleClick} 
                  class="sidebar-toggle flex h-8 w-8 shrink-0 items-center justify-center rounded-md hover:bg-sidebar-accent transition-all duration-200"
                  title="Collapse sidebar"
                  aria-label="Collapse sidebar"
                >
                  <PanelLeftIcon class="h-4 w-4" />
                </button>
              </div>
            {/if}
          </div>
        </div>
      </Sidebar.MenuItem>
    </Sidebar.Menu>
  </Sidebar.Footer>

  <Sidebar.Rail />
</Sidebar.Root>

<style>
  :global(.group[data-collapsible="icon"]) {
    width: 4rem; /* 64px - optimal width for collapsed state with better icon spacing */
  }
  
  :global(.group[data-collapsible="icon"]) :global(.group-data-\[collapsible\=icon\]\:hidden) {
    display: none;
  }

  /* Enhanced styles for collapsed menu items */
  :global(.group[data-collapsible="icon"]) :global([data-sidebar="menu-button"]) {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  /* Remove any pseudo-element content that adds > symbol */
  :global(.group[data-collapsible="icon"]) :global([data-sidebar="menu-button"]::before),
  :global(.group[data-collapsible="icon"]) :global([data-sidebar="menu-button"])::before,
  :global(.group[data-collapsible="icon"]) :global(button::before) {
    content: none !important;
    display: none !important;
  }

  /* Better spacing for collapsed state following 8px grid */
  :global(.group[data-collapsible="icon"]) :global([data-slot="sidebar-content"]) {
    padding-left: 0.5rem;  /* 8px */
    padding-right: 0.5rem; /* 8px */
  }

  /* Footer adjustments for collapsed state */
  :global(.group[data-collapsible="icon"]) :global([data-slot="sidebar-footer"]) {
    padding: 0.5rem; /* 8px - consistent with 8-point grid */
  }

  /* Ensure proper icon sizing and hover states */
  :global(.group[data-collapsible="icon"]) :global([data-sidebar="menu-button"]:hover) {
    transform: scale(1.02);
  }

  /* Active state styling */
  :global(.group[data-collapsible="icon"]) :global([data-active="true"]) {
    background-color: rgb(219 234 254) !important; /* bg-blue-100 */
    color: rgb(29 78 216) !important; /* text-blue-700 */
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    font-weight: 600;
    transform: translateY(-1px);
  }

  /* Better header centering for collapsed state with 8px grid alignment */
  :global(.group[data-collapsible="icon"]) :global([data-slot="sidebar-header"]) {
    padding-left: 0.5rem;  /* 8px */
    padding-right: 0.5rem; /* 8px */
  }
</style>
