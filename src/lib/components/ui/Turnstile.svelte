<script lang="ts">
  import { onMount } from 'svelte';
  import { browser } from '$app/environment';
  import { getEnvironmentConfig } from '$lib/utils/environment';

  interface Props {
    onSuccess?: (token: string) => void;
    onError?: () => void;
    onExpired?: () => void;
  }

  let { onSuccess = () => {}, onError = () => {}, onExpired = () => {} }: Props = $props();

  let turnstileContainer = $state<HTMLDivElement>();
  let turnstileLoaded = $state(false);

  onMount(() => {
    if (!browser) return;

    // Check if script already exists
    if (document.querySelector('script[src*="turnstile"]')) {
      turnstileLoaded = true;
      renderTurnstile();
      return;
    }

    // Load Turnstile script
    const script = document.createElement('script');
    script.src = 'https://challenges.cloudflare.com/turnstile/v0/api.js';
    script.async = true;
    script.defer = true;
    
    script.onload = () => {
      turnstileLoaded = true;
      renderTurnstile();
    };

    script.onerror = () => {
      console.error('Failed to load Turnstile script');
      onError();
    };

    document.head.appendChild(script);
  });

  function renderTurnstile() {
    if (!turnstileLoaded || !turnstileContainer || !window.turnstile) {
      console.log('Turnstile render failed:', { turnstileLoaded, turnstileContainer: !!turnstileContainer, turnstile: !!window.turnstile });
      return;
    }

    try {
      // Use the environment config utility instead of direct import.meta.env access
      const config = getEnvironmentConfig();
      const siteKey = config.turnstile.siteKey;
      console.log('Rendering Turnstile with key:', siteKey);
      
      if (!siteKey) {
        console.error('Turnstile site key not found in environment variables');
        onError();
        return;
      }
      
      window.turnstile.render(turnstileContainer, {
        sitekey: siteKey,
        callback: (token) => {
          console.log('Turnstile success callback:', token);
          onSuccess(token);
        },
        'error-callback': () => {
          console.log('Turnstile error callback');
          onError();
        },
        'expired-callback': () => {
          console.log('Turnstile expired callback');
          onExpired();
        },
        theme: 'auto',
        size: 'normal'
      });
    } catch (error) {
      console.error('Turnstile render error:', error);
      onError();
    }
  }
</script>

<div class="turnstile-container">
  <div bind:this={turnstileContainer}></div>
</div>

<style>
  .turnstile-container {
    display: flex;
    justify-content: center;
    align-items: center;
    /* Completely transparent, just centers the widget */
    background: transparent;
    border: none;
    padding: 0;
    margin: 0;
  }
</style>
