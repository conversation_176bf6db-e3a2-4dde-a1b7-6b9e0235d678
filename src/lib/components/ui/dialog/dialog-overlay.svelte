<script lang="ts">
	import { Dialog as DialogPrimitive } from "bits-ui";
	import { cn } from "$lib/utils";
	import type { ClassValue } from "clsx";

	type $$Props = DialogPrimitive.OverlayProps;

	let className: ClassValue = "";
	export { className as class };
</script>

<DialogPrimitive.Overlay
	class={cn(
		"fixed inset-0 z-50 bg-black/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
		className
	)}
	{...$$restProps}
>
	<slot />
</DialogPrimitive.Overlay>