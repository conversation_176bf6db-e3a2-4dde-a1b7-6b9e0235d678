export const SIDEBAR_COOKIE_NAME = "sidebar:state";
export const SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7;
export const SIDEBAR_WIDTH = "15rem"; // 240px - optimal range for content
export const SIDEBAR_WIDTH_MOBILE = "18rem"; // Keep current mobile width
export const SIDEBAR_WIDTH_ICON = "4rem"; // 64px - better proportions for icons
export const SIDEBAR_KEYBOARD_SHORTCUT = "b";

// Animation and spacing constants following 8-point grid system
export const SIDEBAR_ANIMATION_DURATION = "300ms";
export const SIDEBAR_ANIMATION_EASING = "cubic-bezier(0.4, 0, 0.2, 1)";

// 8-point grid spacing system
export const SPACING = {
  xs: "0.25rem", // 4px
  sm: "0.5rem",  // 8px
  md: "1rem",    // 16px
  lg: "1.5rem",  // 24px
  xl: "2rem"     // 32px
} as const;
