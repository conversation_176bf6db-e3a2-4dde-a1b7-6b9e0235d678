import { IsMobile } from "$lib/hooks/is-mobile.svelte.js";
import { getContext, setContext } from "svelte";
import { SIDEBAR_KEYBOARD_SHORTCUT } from "./constants.js";

type Getter<T> = () => T;

export type SidebarStateProps = {
	/**
	 * A getter function that returns the current open state of the sidebar.
	 * We use a getter function here to support `bind:open` on the `Sidebar.Provider`
	 * component.
	 */
	open: Getter<boolean>;

	/**
	 * A function that sets the open state of the sidebar. To support `bind:open`, we need
	 * a source of truth for changing the open state to ensure it will be synced throughout
	 * the sub-components and any `bind:` references.
	 */
	setOpen: (open: boolean) => void;
};

class SidebarState {
	readonly props: SidebarStateProps;
	open = $derived.by(() => this.props.open());
	openMobile = $state(false);
	setOpen: SidebarStateProps["setOpen"];
	#isMobile: IsMobile;
	state = $derived.by(() => (this.open ? "expanded" : "collapsed"));

	constructor(props: SidebarStateProps) {
		this.setOpen = props.setOpen;
		this.#isMobile = new IsMobile();
		this.props = props;
	}

	// Convenience getter for checking if the sidebar is mobile
	// without this, we would need to use `sidebar.isMobile.current` everywhere
	get isMobile() {
		return this.#isMobile.current;
	}

	// Event handler to apply to the `<svelte:window>`
	handleShortcutKeydown = (e: KeyboardEvent) => {
		// Toggle sidebar with Ctrl/Cmd + B
		if (e.key === SIDEBAR_KEYBOARD_SHORTCUT && (e.metaKey || e.ctrlKey)) {
			e.preventDefault();
			this.toggle();
			return;
		}
		
		// Escape key to close mobile sidebar
		if (e.key === 'Escape' && this.#isMobile.current && this.openMobile) {
			e.preventDefault();
			this.setOpenMobile(false);
			return;
		}
		
		// Arrow key navigation within sidebar (when focused)
		if (document.activeElement?.closest('[data-slot="sidebar-root"]')) {
			this.handleArrowNavigation(e);
		}
	};

	// Enhanced arrow key navigation for better accessibility
	handleArrowNavigation = (e: KeyboardEvent) => {
		const sidebarRoot = document.querySelector('[data-slot="sidebar-root"]');
		if (!sidebarRoot) return;

		const menuButtons = Array.from(
			sidebarRoot.querySelectorAll('[data-sidebar="menu-button"]:not([disabled])')
		) as HTMLElement[];

		if (menuButtons.length === 0) return;

		const currentFocused = document.activeElement as HTMLElement;
		const currentIndex = menuButtons.indexOf(currentFocused);

		let nextIndex = currentIndex;

		switch (e.key) {
			case 'ArrowDown':
				e.preventDefault();
				nextIndex = currentIndex < menuButtons.length - 1 ? currentIndex + 1 : 0;
				break;
			case 'ArrowUp':
				e.preventDefault();
				nextIndex = currentIndex > 0 ? currentIndex - 1 : menuButtons.length - 1;
				break;
			case 'Home':
				e.preventDefault();
				nextIndex = 0;
				break;
			case 'End':
				e.preventDefault();
				nextIndex = menuButtons.length - 1;
				break;
			default:
				return;
		}

		if (nextIndex >= 0 && nextIndex < menuButtons.length) {
			menuButtons[nextIndex].focus();
		}
	};

	setOpenMobile = (value: boolean) => {
		this.openMobile = value;
	};

	toggle = (forceState?: boolean) => {
		if (forceState !== undefined) {
			return this.#isMobile.current
				? (this.openMobile = forceState)
				: this.setOpen(forceState);
		}
		return this.#isMobile.current
			? (this.openMobile = !this.openMobile)
			: this.setOpen(!this.open);
	};
}

const SYMBOL_KEY = "scn-sidebar";

/**
 * Instantiates a new `SidebarState` instance and sets it in the context.
 *
 * @param props The constructor props for the `SidebarState` class.
 * @returns  The `SidebarState` instance.
 */
export function setSidebar(props: SidebarStateProps): SidebarState {
	return setContext(Symbol.for(SYMBOL_KEY), new SidebarState(props));
}

/**
 * Retrieves the `SidebarState` instance from the context. This is a class instance,
 * so you cannot destructure it.
 * @returns The `SidebarState` instance.
 */
export function useSidebar(): SidebarState {
	return getContext(Symbol.for(SYMBOL_KEY));
}
