<script lang="ts">
  import { Card, CardContent } from '$lib/components/ui/card';
  import * as Tabs from '$lib/components/ui/tabs';
  import LoginForm from '$lib/components/auth/LoginForm.svelte';
  import RegisterForm from '$lib/components/auth/RegisterForm.svelte';
  import { page } from '$app/stores';
  import { goto } from '$app/navigation';

  interface Props {
    turnstileToken: string;
  }

  let { turnstileToken }: Props = $props();

  // Handle tab changes with URL updates
  function handleTabChange(value: string) {
    // We're staying on the same page, just switching tabs
    // No need to change URL since we want single-page auth
  }

  // Default to login tab
  let activeTab = $state('login');
</script>

<div class="auth-container">
  <Card class="auth-card">
    <CardContent class="auth-content">
      <Tabs.Root value={activeTab} onValueChange={handleTabChange} class="auth-tabs">
        <Tabs.List class="auth-tabs-list">
          <Tabs.Trigger value="login" class="auth-tab-trigger">
            Sign In
          </Tabs.Trigger>
          <Tabs.Trigger value="register" class="auth-tab-trigger">
            Create Account
          </Tabs.Trigger>
        </Tabs.List>
        
        <div class="auth-tab-content">
          <Tabs.Content value="login" class="auth-form-content">
            <LoginForm {turnstileToken} />
          </Tabs.Content>
          
          <Tabs.Content value="register" class="auth-form-content">
            <RegisterForm {turnstileToken} />
          </Tabs.Content>
        </div>
      </Tabs.Root>
    </CardContent>
  </Card>
</div>

<style>
  .auth-container {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
    animation: slideIn 0.6s ease-out;
    transform-origin: center;
  }

  @keyframes slideIn {
    from {
      opacity: 0;
      transform: translateY(20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  :global(.auth-card) {
    background: white;
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
  }

  :global(.auth-card:hover) {
    box-shadow: 
      0 20px 40px -4px rgba(0, 0, 0, 0.15),
      0 8px 16px -4px rgba(0, 0, 0, 0.1),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
  }

  :global(.dark .auth-card) {
    background: rgba(2, 6, 23, 0.95);
    border: 1px solid rgba(30, 41, 59, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(148, 163, 184, 0.1);
  }

  :global(.dark .auth-card:hover) {
    box-shadow: 
      0 20px 40px -4px rgba(0, 0, 0, 0.6),
      0 8px 16px -4px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(148, 163, 184, 0.2);
  }

  :global(.auth-content) {
    padding-top: 1.5rem;
    animation: fadeInUp 0.8s ease-out 0.4s both;
  }

  :global(.auth-tabs-list) {
    grid-template-columns: 1fr 1fr;
    width: 100%;
    background: linear-gradient(135deg, hsl(var(--muted)), rgba(0, 0, 0, 0.05));
    border-radius: 12px;
    padding: 6px;
    margin-bottom: 2rem;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
  }

  :global(.auth-tabs-list::before) {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
  }

  :global(.auth-tab-trigger) {
    font-weight: 600;
    padding: 1rem 1.25rem;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    font-size: 0.95rem;
    letter-spacing: 0.01em;
  }

  :global(.auth-tab-trigger:hover:not([data-state="active"])) {
    background: rgba(255, 255, 255, 0.5);
    transform: translateY(-1px);
    color: hsl(var(--foreground));
  }

  :global(.auth-tab-trigger[data-state="active"]) {
    background: linear-gradient(135deg, white, rgba(255, 255, 255, 0.95));
    color: hsl(var(--primary));
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.12),
      0 1px 3px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.9);
    transform: translateY(-2px);
    font-weight: 700;
  }

  :global(.auth-tab-trigger[data-state="active"]::after) {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 24px;
    height: 2px;
    background: hsl(var(--primary));
    border-radius: 1px;
    animation: tabSlide 0.3s ease-out;
  }

  @keyframes tabSlide {
    from {
      width: 0;
      opacity: 0;
    }
    to {
      width: 24px;
      opacity: 1;
    }
  }

  :global(.dark .auth-tab-trigger[data-state="active"]) {
    background: linear-gradient(135deg, hsl(var(--background)), rgba(0, 0, 0, 0.3));
    color: hsl(var(--primary));
    box-shadow: 
      0 4px 8px rgba(0, 0, 0, 0.3),
      0 1px 3px rgba(0, 0, 0, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
  }

  .auth-tab-content {
    min-height: 280px;
    position: relative;
  }

  :global(.auth-form-content) {
    outline: none;
    animation: formSlideIn 0.5s ease-out;
  }

  @keyframes formSlideIn {
    from {
      opacity: 0;
      transform: translateX(10px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  /* Mobile Optimizations */
  @media (max-width: 640px) {
    .auth-container {
      max-width: 100%;
      padding: 0 1rem;
    }

    :global(.auth-card) {
      border-radius: 12px;
    }

    :global(.auth-header) {
      padding: 1.5rem 1rem 1rem;
    }

    :global(.auth-title) {
      font-size: 1.25rem;
    }

    :global(.auth-content) {
      padding: 1rem;
    }

    :global(.auth-tab-trigger) {
      padding: 0.625rem 0.75rem;
      font-size: 0.875rem;
    }
  }
</style>
