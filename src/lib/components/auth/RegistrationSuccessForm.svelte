<script lang="ts">
  import { goto } from '$app/navigation';
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { CheckCircle, Mail, ArrowLeft } from 'lucide-svelte';

  interface Props {
    email: string;
    turnstileToken: string;
  }

  let { email, turnstileToken }: Props = $props();

  function handleBackToLogin() {
    goto('/');
  }

  function handleResendVerification() {
    goto('/resendverification?email=' + encodeURIComponent(email));
  }
</script>

<div class="auth-container">
  <Card class="auth-card">
    <CardContent class="auth-content">
      <!-- Success State -->
      <div class="success-state">
        <div class="success-icon">
          <CheckCircle size={48} />
        </div>
        
        <div class="success-content">
          <h3 class="success-title">Registration Successful!</h3>
          <p class="success-message">
            We've sent a verification link to <strong>{email}</strong>
          </p>
          <p class="success-message-sub">
            Click the link in your email to verify your account and complete the registration process.
          </p>
        </div>

        <!-- Action Buttons -->
        <div class="action-buttons">
          <Button onclick={handleBackToLogin} class="w-full">
            <ArrowLeft size={16} class="mr-2" />
            Continue to Login
          </Button>
          
          <Button variant="outline" onclick={handleResendVerification} class="w-full">
            <Mail size={16} class="mr-2" />
            Didn't receive the email?
          </Button>
        </div>
      </div>

      <!-- Email Tips -->
      <div class="email-tips">
        <div class="tips-header">
          <svg class="tips-icon" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h4 class="tips-title">Tips for receiving your email:</h4>
        </div>
        <ul class="tips-list">
          <li>Check your spam or junk folder</li>
          <li>Wait up to 5 minutes for delivery</li>
          <li>Make sure {email} is correct</li>
          <li>Add <EMAIL> to your contacts</li>
        </ul>
      </div>

      <!-- Support Link -->
      <div class="back-to-login">
        <p class="support-link">
          Still having trouble? 
          <a href="mailto:<EMAIL>">Contact Support</a>
        </p>
      </div>
    </CardContent>
  </Card>
</div>

<style>
  .auth-container {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
  }

  :global(.auth-card) {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  :global(.dark .auth-card) {
    background: rgba(2, 6, 23, 0.95);
    border: 1px solid rgba(30, 41, 59, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(148, 163, 184, 0.1);
  }

  :global(.auth-content) {
    padding-top: 1.5rem;
  }

  .success-state {
    text-align: center;
    padding: 1rem 0;
    animation: successFadeIn 0.6s ease-out;
  }

  @keyframes successFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: rgb(34, 197, 94);
  }

  .success-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
  }

  .success-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.75rem;
  }

  .success-message {
    color: hsl(var(--muted-foreground));
    line-height: 1.5;
    margin-bottom: 0.5rem;
  }

  .success-message-sub {
    color: hsl(var(--muted-foreground));
    font-size: 0.875rem;
    line-height: 1.5;
  }

  .action-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    margin-bottom: 2rem;
  }

  .email-tips {
    margin-top: 1.5rem;
    padding: 1rem;
    background: rgba(59, 130, 246, 0.05);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
  }

  .tips-header {
    display: flex;
    align-items: flex-start;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
  }

  .tips-icon {
    height: 1.25rem;
    width: 1.25rem;
    color: rgb(59, 130, 246);
    margin-top: 0.125rem;
    flex-shrink: 0;
  }

  .tips-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: rgb(30, 58, 138);
    margin: 0;
  }

  .tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
    color: rgb(30, 64, 175);
    font-size: 0.875rem;
    line-height: 1.5;
    margin-left: 2rem;
  }

  .tips-list li {
    margin-bottom: 0.25rem;
    position: relative;
  }

  .tips-list li::before {
    content: '•';
    position: absolute;
    left: -1rem;
    color: rgb(59, 130, 246);
  }

  .back-to-login {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid hsl(var(--border));
  }

  .support-link {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    margin: 0;
  }

  .support-link a {
    font-weight: 600;
    color: hsl(var(--primary));
    text-decoration: none;
  }

  .support-link a:hover {
    text-decoration: underline;
  }

  /* Mobile Optimizations */
  @media (max-width: 640px) {
    .auth-container {
      max-width: 100%;
      padding: 0 1rem;
    }

    :global(.auth-card) {
      border-radius: 16px;
    }

    :global(.auth-content) {
      padding: 1rem;
    }

    .success-message,
    .success-message-sub {
      font-size: 0.875rem;
    }
  }
</style>
