<script lang="ts">
  import { goto } from '$app/navigation';
  import { page } from '$app/stores';
  import { authActions, isAuthenticated } from '$lib/stores/auth.js';
  import { onMount } from 'svelte';
  import toast from 'svelte-5-french-toast';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Eye, EyeOff } from 'lucide-svelte';

  interface Props {
    turnstileToken: string;
  }

  let { turnstileToken }: Props = $props();

  let email = $state('');
  let password = $state('');
  let showPassword = $state(false);
  let isLoading = $state(false);
  let verificationMessage = $state('');
  let loginInProgress = $state(false);

  // Check for verification messages from URL params
  onMount(() => {
    const message = $page.url.searchParams.get('message');
    const urlEmail = $page.url.searchParams.get('email');
    
    if (urlEmail) email = urlEmail;
    
    if (message) {
      switch (message) {
        case 'verified':
          verificationMessage = 'Email verified successfully! Please log in to access your account.';
          toast.success('Email verified successfully!');
          break;
        case 'already_verified':
          verificationMessage = 'Your email is already verified. Please log in to continue.';
          break;
        case 'failed':
          verificationMessage = 'Email verification failed. Please try logging in or request a new verification link.';
          break;
      }
    }

    // Check if already authenticated
    const unsubscribe = isAuthenticated.subscribe((authenticated) => {
      if (authenticated && !loginInProgress) {
        const returnTo = $page.url.searchParams.get('return') || '/dashboard';
        toast.success('Already signed in, redirecting...');
        goto(returnTo);
      }
    });

    return unsubscribe;
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    if (!email || !password) {
      toast.error('Please fill in all fields');
      return;
    }

    isLoading = true;
    loginInProgress = true;

    try {
      const result = await authActions.signIn(email, password);
      
      if (result.error) {
        toast.error(result.error.message || 'Login failed');
        return;
      }

      toast.success('Login successful!');
      const returnTo = $page.url.searchParams.get('return') || '/dashboard';
      goto(returnTo);
      
    } catch (error) {
      console.error('Login error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
      setTimeout(() => loginInProgress = false, 1000);
    }
  }
</script>

<div class="login-form">
  <!-- Verification Message -->
  {#if verificationMessage}
    <div class="verification-message {verificationMessage.includes('successfully') ? 'success' : 'info'}">
      {verificationMessage}
    </div>
  {/if}

  <form onsubmit={handleSubmit} class="auth-form">
    <!-- Email Field -->
    <div class="form-group">
      <Label for="email">Email address</Label>
      <Input
        id="email"
        type="email"
        bind:value={email}
        placeholder="Enter your email"
        disabled={isLoading}
        required
      />
    </div>

    <!-- Password Field -->
    <div class="form-group">
      <Label for="password">Password</Label>
      <div class="password-wrapper">
        <Input
          id="password"
          type={showPassword ? 'text' : 'password'}
          bind:value={password}
          placeholder="Enter your password"
          disabled={isLoading}
          required
          class="pr-10"
        />
        <button
          type="button"
          class="password-toggle"
          onclick={() => showPassword = !showPassword}
          aria-label={showPassword ? 'Hide password' : 'Show password'}
        >
          {#if showPassword}
            <EyeOff size={18} />
          {:else}
            <Eye size={18} />
          {/if}
        </button>
      </div>
    </div>

    <!-- Forgot Password Link -->
    <div class="forgot-password">
      <a href="/forgotpassword" class="forgot-link">
        Forgot your password?
      </a>
    </div>

    <!-- Submit Button -->
    <Button type="submit" disabled={isLoading}>
      {#if isLoading}
        <div class="loading-spinner"></div>
        Signing in...
      {:else}
        Sign in
      {/if}
    </Button>
  </form>
</div>

<style>
  .login-form {
    width: 100%;
    animation: formFadeIn 0.6s ease-out;
  }

  @keyframes formFadeIn {
    from {
      opacity: 0;
      transform: translateY(15px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .verification-message {
    padding: 1rem 1.25rem;
    border-radius: 12px;
    font-size: 0.875rem;
    text-align: center;
    margin-bottom: 1.5rem;
    border: 1px solid;
    animation: messageSlideDown 0.5s ease-out;
    backdrop-filter: blur(8px);
    position: relative;
    overflow: hidden;
  }

  @keyframes messageSlideDown {
    from {
      opacity: 0;
      transform: translateY(-20px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .verification-message::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    animation: shimmer 2s ease-in-out infinite;
  }

  @keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  .verification-message.success {
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    color: rgb(22, 163, 74);
    border-color: rgba(34, 197, 94, 0.3);
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.15);
  }

  .verification-message.info {
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    color: rgb(37, 99, 235);
    border-color: rgba(59, 130, 246, 0.3);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    animation: fieldSlideIn 0.6s ease-out;
    animation-fill-mode: both;
  }

  .form-group:nth-child(1) { animation-delay: 0.1s; }
  .form-group:nth-child(2) { animation-delay: 0.2s; }
  .form-group:nth-child(3) { animation-delay: 0.3s; }

  @keyframes fieldSlideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .password-wrapper {
    position: relative;
    transition: all 0.3s ease;
  }

  .password-wrapper:focus-within {
    transform: translateY(-1px);
  }

  .password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    padding: 0.375rem;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .password-toggle:hover {
    color: hsl(var(--foreground));
    background: linear-gradient(135deg, hsl(var(--muted)), rgba(0, 0, 0, 0.05));
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .password-toggle:active {
    transform: translateY(-50%) scale(0.95);
  }

  .forgot-password {
    text-align: right;
    margin-top: -0.5rem;
  }

  .forgot-link {
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    font-weight: 500;
    transition: all 0.3s ease;
  }

  .forgot-link:hover {
    text-decoration: underline;
    opacity: 0.8;
  }

  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2.5px solid transparent;
    border-top: 2.5px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    from { opacity: 1; }
    to { opacity: 0.7; }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* Mobile Optimizations */
  @media (max-width: 640px) {
    .auth-form {
      gap: 1rem;
    }

    :global(.form-input),
    :global(.submit-button) {
      height: 48px;
      font-size: 16px; /* Prevents zoom on iOS */
    }

    .password-toggle {
      right: 1rem;
    }
  }
</style>
