<script lang="ts">
  import { goto } from '$app/navigation';
  import { nhost } from '$lib/stores/nhost.js';
  import toast from 'svelte-5-french-toast';
  import { browser } from '$app/environment';
  import { Card, CardContent } from '$lib/components/ui/card';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { CheckCircle, ArrowLeft } from 'lucide-svelte';

  interface Props {
    turnstileToken: string;
  }

  let { turnstileToken }: Props = $props();

  let email = $state('');
  let isLoading = $state(false);
  let isEmailSent = $state(false);
  let emailError = $state('');
  let attemptCount = $state(0);
  let maxAttempts = 3;
  let rateLimitKey = '';

  function getRateLimitKey() {
    const today = new Date().toDateString();
    return `verification_attempts_${today}`;
  }

  function checkRateLimit(): boolean {
    if (!browser) return true;
    
    rateLimitKey = getRateLimitKey();
    const stored = localStorage.getItem(rateLimitKey);
    attemptCount = stored ? parseInt(stored) : 0;
    
    return attemptCount < maxAttempts;
  }

  function incrementAttemptCount() {
    if (!browser) return;
    
    attemptCount++;
    localStorage.setItem(rateLimitKey, attemptCount.toString());
  }

  $effect(() => {
    if (email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      emailError = !emailRegex.test(email) ? 'Please enter a valid email address' : '';
    } else {
      emailError = '';
    }
  });

  async function handleSubmit(event: Event) {
    event.preventDefault();

    if (!email || emailError) {
      toast.error('Please enter a valid email address');
      return;
    }

    if (!checkRateLimit()) {
      toast.error(`You've reached the daily limit of ${maxAttempts} verification attempts.`);
      return;
    }

    isLoading = true;

    try {
      const result = await nhost.auth.signUp({
        email: email,
        password: '',
        options: {
          allowSignUp: false
        }
      });

      incrementAttemptCount();

      if (result.error) {
        if (result.error.message?.includes('User already registered')) {
          isEmailSent = true;
          toast.success('Verification email sent! Please check your inbox.');
        } else {
          toast.error(result.error.message || 'Failed to send verification email');
        }
      } else {
        isEmailSent = true;
        toast.success('Verification email sent! Please check your inbox.');
      }

    } catch (error) {
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      isLoading = false;
    }
  }

  function handleBackToLogin() {
    goto('/');
  }

  if (browser) {
    checkRateLimit();
  }
</script>

<div class="auth-container">
  <Card class="auth-card">
    <CardContent class="auth-content">
      {#if isEmailSent}
        <div class="success-state">
          <div class="success-icon">
            <CheckCircle size={48} />
          </div>
          
          <div class="success-content">
            <h3 class="success-title">Email sent successfully!</h3>
            <p class="success-message">
              We've sent a new verification link to <strong>{email}</strong>. 
              Please check your inbox and spam folder.
            </p>
            
            <div class="success-actions">
              <Button onclick={handleBackToLogin} class="w-full">
                <ArrowLeft size={16} class="mr-2" />
                Back to Login
              </Button>
            </div>
          </div>
        </div>

        <div class="email-tips">
          <h4 class="tips-title">Tips for receiving your email:</h4>
          <ul class="tips-list">
            <li>Check your spam/junk folder</li>
            <li>Wait up to 5 minutes for delivery</li>
            <li>Add <EMAIL> to your contacts</li>
            <li>Check that your email address is correct</li>
          </ul>
        </div>

      {:else}
        <form class="auth-form" onsubmit={handleSubmit}>
          <div class="form-group">
            <Label for="email">Email address</Label>
            <Input
              id="email"
              type="email"
              bind:value={email}
              placeholder="Enter your email address"
              disabled={isLoading}
              class={emailError ? 'border-red-500' : ''}
            />
            {#if emailError}
              <p class="text-sm text-red-600">{emailError}</p>
            {/if}
          </div>

          {#if attemptCount > 0}
            <div class="rate-limit-info">
              <p>Attempts used today: {attemptCount}/{maxAttempts}</p>
              {#if attemptCount >= maxAttempts}
                <p class="rate-limit-error">
                  Daily limit reached. Please contact support for assistance.
                </p>
              {/if}
            </div>
          {/if}

          <Button 
            type="submit" 
            class="w-full" 
            disabled={isLoading || emailError !== '' || attemptCount >= maxAttempts}
          >
            {#if isLoading}
              <div class="loading-spinner"></div>
              Sending...
            {:else if attemptCount >= maxAttempts}
              Daily Limit Reached
            {:else}
              Send Verification Email
            {/if}
          </Button>
        </form>
      {/if}

      <div class="back-to-login">
        <a href="/" class="back-link">
          <ArrowLeft size={16} />
          Back to login
        </a>
        
        <p class="support-link">
          Need help? 
          <a href="mailto:<EMAIL>">Contact Support</a>
        </p>
      </div>
    </CardContent>
  </Card>
</div>

<style>
  .auth-container {
    width: 100%;
    max-width: 420px;
    margin: 0 auto;
  }

  :global(.auth-card) {
    background: rgba(255, 255, 255, 0.95);
    border: 1px solid rgba(226, 232, 240, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 0 1px rgba(0, 0, 0, 0.05);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
  }

  :global(.dark .auth-card) {
    background: rgba(2, 6, 23, 0.95);
    border: 1px solid rgba(30, 41, 59, 0.8);
    box-shadow: 
      0 10px 25px -3px rgba(0, 0, 0, 0.4),
      0 4px 6px -2px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(148, 163, 184, 0.1);
  }

  :global(.auth-content) {
    padding-top: 1.5rem;
  }

  .success-state {
    text-align: center;
    padding: 1rem 0;
    animation: successFadeIn 0.6s ease-out;
  }

  @keyframes successFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .success-icon {
    display: flex;
    justify-content: center;
    margin-bottom: 1.5rem;
    color: rgb(34, 197, 94);
  }

  .success-content {
    display: flex;
    flex-direction: column;
    gap: 1rem;
  }

  .success-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.75rem;
  }

  .success-message {
    color: hsl(var(--muted-foreground));
    line-height: 1.5;
    margin-bottom: 1.5rem;
  }

  .success-actions {
    margin-top: 2rem;
  }

  .email-tips {
    margin-top: 2rem;
    padding: 1rem;
    background: hsl(var(--muted));
    border-radius: 8px;
    border: 1px solid hsl(var(--border));
  }

  .tips-title {
    font-size: 0.875rem;
    font-weight: 600;
    color: hsl(var(--foreground));
    margin-bottom: 0.75rem;
  }

  .tips-list {
    list-style: none;
    padding: 0;
    margin: 0;
    color: hsl(var(--muted-foreground));
    font-size: 0.8rem;
    line-height: 1.5;
  }

  .tips-list li {
    margin-bottom: 0.25rem;
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    animation: formFadeIn 0.8s ease-out 0.2s both;
  }

  @keyframes formFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .rate-limit-info {
    font-size: 0.875rem;
    color: hsl(var(--muted-foreground));
    background: hsl(var(--muted));
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid hsl(var(--border));
  }

  .rate-limit-error {
    color: rgb(239, 68, 68);
    font-weight: 600;
    margin-top: 0.5rem;
  }

  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2.5px solid transparent;
    border-top: 2.5px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  .back-to-login {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid hsl(var(--border));
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }

  .back-link {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: hsl(var(--primary));
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
  }

  .back-link:hover {
    background: hsl(var(--muted));
  }

  .support-link {
    font-size: 0.75rem;
    color: hsl(var(--muted-foreground));
    margin: 0;
  }

  .support-link a {
    font-weight: 600;
    color: hsl(var(--primary));
    text-decoration: none;
  }

  .support-link a:hover {
    text-decoration: underline;
  }

  @media (max-width: 640px) {
    .auth-container {
      max-width: 100%;
      padding: 0 1rem;
    }

    :global(.auth-card) {
      border-radius: 16px;
    }

    :global(.auth-content) {
      padding: 1rem;
    }

    .success-message {
      font-size: 0.875rem;
    }
  }
</style>
