<script lang="ts">
  import { onMount } from 'svelte';
  import { turnstileState } from '$lib/stores/turnstile';
  import Turnstile from '$lib/components/ui/Turnstile.svelte';
  import type { Snippet } from 'svelte';

  interface Props {
    title: string;
    description: string;
    children: Snippet<[string]>; // Snippet that receives turnstileToken as parameter
  }

  let { title, description, children }: Props = $props();

  // State
  let showForm = $state(false);
  let turnstileToken = $state('');

  onMount(() => {
    // Check if already verified
    if (turnstileState.isTokenValid()) {
      const token = turnstileState.getToken();
      if (token) {
        turnstileToken = token;
        showForm = true;
        return;
      }
    }
    
    // Initialize turnstile state for new verification
    turnstileState.init();
  });

  function handleTurnstileSuccess(token: string) {
    console.log('ProtectedAuthPage: Turnstile success:', token);
    turnstileState.setVerified(token);
    turnstileToken = token;
    showForm = true;
  }

  function handleTurnstileError() {
    console.log('ProtectedAuthPage: Turnstile error');
    showForm = false;
    turnstileToken = '';
  }
</script>

<svelte:head>
  <title>{title} - SourceFlex</title>
  <meta name="description" content={description} />
</svelte:head>

{#if !showForm}
  <!-- Phase 1: ONLY Turnstile Widget (transparent, centered, clean) -->
  <div class="turnstile-phase">
    <Turnstile 
      onSuccess={handleTurnstileSuccess}
      onError={handleTurnstileError}
    />
  </div>
{:else}
  <!-- Phase 2: Full Auth Page (with logo, title, tabs) -->
  <div class="auth-phase">
    {@render children(turnstileToken)}
  </div>
{/if}

<style>
  .turnstile-phase {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: transparent;
    animation: turnstileFadeIn 0.8s ease-out;
    position: relative;
  }

  @keyframes turnstileFadeIn {
    from {
      opacity: 0;
      transform: scale(0.9);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .turnstile-phase::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(99, 102, 241, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    animation: pulseGlow 3s ease-in-out infinite;
    z-index: -1;
  }

  @keyframes pulseGlow {
    0%, 100% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.5;
    }
    50% {
      transform: translate(-50%, -50%) scale(1.2);
      opacity: 0.8;
    }
  }

  .auth-phase {
    width: 100%;
    animation: authFadeIn 0.6s ease-out;
  }

  @keyframes authFadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
</style>
