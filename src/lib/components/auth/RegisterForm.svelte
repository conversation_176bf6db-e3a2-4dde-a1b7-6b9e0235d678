<script lang="ts">
  import { goto } from '$app/navigation';
  import { authActions } from '$lib/stores/auth.js';
  import { getEnvironmentConfig } from '$lib/utils/environment.js';
  import toast from 'svelte-5-french-toast';
  import { Button } from '$lib/components/ui/button';
  import { Input } from '$lib/components/ui/input';
  import { Label } from '$lib/components/ui/label';
  import { Eye, EyeOff } from 'lucide-svelte';

  interface Props {
    turnstileToken: string;
  }

  const { turnstileToken }: Props = $props();

  let email = $state('');
  let password = $state('');
  let confirmPassword = $state('');
  let showPassword = $state(false);
  let showConfirmPassword = $state(false);
  let isLoading = $state(false);

  // Enhanced password validation for nHost
  function validatePassword(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('At least 8 characters');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('One lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('One uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('One number');
    }
    
    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) {
      errors.push('One special character');
    }
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async function handleSubmit(event: SubmitEvent) {
    event.preventDefault();
    
    if (!email || !password || !confirmPassword) {
      toast.error('Please fill in all fields');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Passwords do not match');
      return;
    }

    // Enhanced password validation
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      toast.error(`Password must contain: ${passwordValidation.errors.join(', ')}`);
      return;
    }

    isLoading = true;

    try {
     // Get environment config for proper redirect URL
     const envConfig = getEnvironmentConfig();
     
     // Add redirectTo parameter for email verification
     const signUpOptions = {
       redirectTo: `${envConfig.domain}/verify`
     };
     
     console.log('🔄 RegisterForm: SignUp with redirectTo:', signUpOptions.redirectTo);
     
     const result = await authActions.signUp(email, password, signUpOptions, turnstileToken);
      
      if (result.error) {
        // Enhanced error handling for nHost specific errors
        let errorMessage = result.error.message || 'Registration failed';
        
        // Handle specific nHost error cases
        if (result.error.message?.toLowerCase().includes('pwned') || 
            result.error.message?.toLowerCase().includes('compromised') ||
            result.error.message?.toLowerCase().includes('breach')) {
          errorMessage = 'This password has been found in data breaches. Please choose a more unique password.';
        } else if (result.error.message?.toLowerCase().includes('password')) {
          errorMessage = 'Password does not meet security requirements. Use 8+ characters with uppercase, lowercase, number, and symbol.';
        } else if (result.error.message?.toLowerCase().includes('email')) {
          errorMessage = 'This email address cannot be used. Please try a different email.';
        } else if (result.error.message?.toLowerCase().includes('blocked')) {
          errorMessage = 'This email domain is not allowed for registration.';
        }
        
        console.error('SignUp error details:', result.error);
        toast.error(errorMessage);
        return;
      }

      toast.success('Registration successful! Please check your email to verify your account.');
      goto('/registration-success?email=' + encodeURIComponent(email));
      
    } catch (error) {
      console.error('Registration error:', error);
      toast.error('An unexpected error occurred');
    } finally {
      isLoading = false;
    }
  }
</script>

<div class="register-form">
  <form onsubmit={handleSubmit} class="auth-form">
    <!-- Email Field -->
    <div class="form-group">
      <Label for="register-email" class="form-label">Email address</Label>
      <Input
        id="register-email"
        type="email"
        bind:value={email}
        placeholder="Enter your email"
        disabled={isLoading}
        required
        class="form-input"
      />
    </div>

    <!-- Password Field -->
    <div class="form-group">
      <Label for="register-password" class="form-label">Password</Label>
      <div class="password-wrapper">
        <Input
          id="register-password"
          type={showPassword ? 'text' : 'password'}
          bind:value={password}
          placeholder="Create a secure password (8+ chars, mixed case, number, symbol)"
          disabled={isLoading}
          required
          minlength={8}
          class="form-input password-input"
        />
        <button
          type="button"
          class="password-toggle"
          onclick={() => showPassword = !showPassword}
          aria-label={showPassword ? 'Hide password' : 'Show password'}
        >
          {#if showPassword}
            <EyeOff size={18} />
          {:else}
            <Eye size={18} />
          {/if}
        </button>
      </div>
    </div>

    <!-- Confirm Password Field -->
    <div class="form-group">
      <Label for="confirm-password" class="form-label">Confirm password</Label>
      <div class="password-wrapper">
        <Input
          id="confirm-password"
          type={showConfirmPassword ? 'text' : 'password'}
          bind:value={confirmPassword}
          placeholder="Confirm your password"
          disabled={isLoading}
          required
          class="form-input password-input"
        />
        <button
          type="button"
          class="password-toggle"
          onclick={() => showConfirmPassword = !showConfirmPassword}
          aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
        >
          {#if showConfirmPassword}
            <EyeOff size={18} />
          {:else}
            <Eye size={18} />
          {/if}
        </button>
      </div>
    </div>

    <!-- Enhanced Password Requirements -->
    {#if password && password.length > 0}
      <div class="password-validation">
        <div class="validation-item {password.length >= 8 ? 'valid' : 'invalid'}">
          {password.length >= 8 ? '✓' : '○'} 8+ characters
        </div>
        <div class="validation-item {(/[A-Z]/.test(password) && /[a-z]/.test(password)) ? 'valid' : 'invalid'}">
          {(/[A-Z]/.test(password) && /[a-z]/.test(password)) ? '✓' : '○'} Upper & lowercase
        </div>
        <div class="validation-item {(/\d/.test(password)) ? 'valid' : 'invalid'}">
          {(/\d/.test(password)) ? '✓' : '○'} Number
        </div>
        <div class="validation-item {(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) ? 'valid' : 'invalid'}">
          {(/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) ? '✓' : '○'} Special character
        </div>
        {#if confirmPassword && confirmPassword.length > 0}
          <div class="validation-item {password === confirmPassword ? 'valid' : 'invalid'}">
            {password === confirmPassword ? '✓' : '○'} Passwords match
          </div>
        {/if}
        <div class="validation-item info">
          💡 Use a unique password not found in data breaches
        </div>
      </div>
    {/if}

    <!-- Submit Button -->
    <Button type="submit" disabled={isLoading}>
      {#if isLoading}
        <div class="loading-spinner"></div>
        Creating account...
      {:else}
        Create account
      {/if}
    </Button>
  </form>
</div>

<style>
  .register-form {
    width: 100%;
    animation: formFadeIn 0.6s ease-out;
  }

  @keyframes formFadeIn {
    from {
      opacity: 0;
      transform: translateY(15px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .auth-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-group {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
    animation: fieldSlideIn 0.6s ease-out;
    animation-fill-mode: both;
  }

  .form-group:nth-child(1) { animation-delay: 0.1s; }
  .form-group:nth-child(2) { animation-delay: 0.2s; }
  .form-group:nth-child(3) { animation-delay: 0.3s; }

  @keyframes fieldSlideIn {
    from {
      opacity: 0;
      transform: translateX(-20px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  :global(.form-label) {
    font-weight: 600;
    font-size: 0.875rem;
    color: hsl(var(--foreground));
    transition: color 0.3s ease;
    position: relative;
  }

  :global(.form-input) {
    height: 46px;
    border-radius: 10px;
    border: 2px solid hsl(var(--border));
    background: linear-gradient(135deg, hsl(var(--background)), rgba(255, 255, 255, 0.5));
    color: hsl(var(--foreground));
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 0.875rem;
    padding: 0 1rem;
    backdrop-filter: blur(2px);
  }

  :global(.form-input:focus) {
    outline: none;
    border-color: hsl(var(--primary));
    background: white;
    box-shadow: 
      0 0 0 4px rgba(99, 102, 241, 0.1),
      0 8px 25px -5px rgba(99, 102, 241, 0.2);
    transform: translateY(-2px);
  }

  :global(.form-input:hover:not(:focus)) {
    border-color: rgba(99, 102, 241, 0.4);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
    transform: translateY(-1px);
  }

  .password-wrapper {
    position: relative;
    transition: all 0.3s ease;
  }

  .password-wrapper:focus-within {
    transform: translateY(-1px);
  }

  :global(.password-input) {
    padding-right: 2.75rem;
  }

  .password-toggle {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: hsl(var(--muted-foreground));
    cursor: pointer;
    padding: 0.375rem;
    border-radius: 6px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .password-toggle:hover {
    color: hsl(var(--foreground));
    background: linear-gradient(135deg, hsl(var(--muted)), rgba(0, 0, 0, 0.05));
    transform: translateY(-50%) scale(1.05);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .password-toggle:active {
    transform: translateY(-50%) scale(0.95);
  }

  .password-validation {
    display: flex;
    gap: 0.75rem;
    font-size: 0.75rem;
    margin-top: 0.75rem;
    flex-wrap: wrap;
    animation: validationSlideIn 0.4s ease-out;
  }

  @keyframes validationSlideIn {
    from {
      opacity: 0;
      transform: translateY(-10px);
      max-height: 0;
    }
    to {
      opacity: 1;
      transform: translateY(0);
      max-height: 50px;
    }
  }

  .validation-item {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 0.25rem;
  }

  .validation-item.valid {
    color: rgb(22, 163, 74);
    background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(34, 197, 94, 0.05));
    border: 1px solid rgba(34, 197, 94, 0.2);
    transform: scale(1.05);
    animation: validationSuccess 0.3s ease-out;
  }

  .validation-item.invalid {
    color: hsl(var(--muted-foreground));
    background: rgba(0, 0, 0, 0.02);
    border: 1px solid rgba(0, 0, 0, 0.1);
  }

  .validation-item.info {
    color: rgb(59, 130, 246);
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(59, 130, 246, 0.05));
    border: 1px solid rgba(59, 130, 246, 0.2);
    font-size: 0.7rem;
  }

  @keyframes validationSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1.05); }
  }

  .loading-spinner {
    width: 18px;
    height: 18px;
    border: 2.5px solid transparent;
    border-top: 2.5px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite, pulse 2s ease-in-out infinite alternate;
  }

  @keyframes pulse {
    from { opacity: 1; }
    to { opacity: 0.7; }
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }

  /* Mobile Optimizations */
  @media (max-width: 640px) {
    .auth-form {
      gap: 1.25rem;
    }

    :global(.form-input) {
      height: 50px;
      font-size: 16px; /* Prevents zoom on iOS */
      border-radius: 12px;
    }

    .password-toggle {
      right: 1rem;
      padding: 0.5rem;
    }

    .password-validation {
      padding: 0.75rem;
      gap: 0.5rem;
    }

    .validation-item {
      font-size: 0.7rem;
      padding: 0.2rem 0.4rem;
    }
  }
</style>
