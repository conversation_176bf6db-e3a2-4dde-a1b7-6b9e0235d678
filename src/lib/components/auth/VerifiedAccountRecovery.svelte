<script lang="ts">
  import { goto } from '$app/navigation';

  interface Props {
    email: string;
    onBackToForm?: () => void;
  }

  let { email, onBackToForm }: Props = $props();

  function handleGoToLogin() {
    // Pre-populate email in login form
    goto(`/?email=${encodeURIComponent(email)}`);
  }

  function handleResetPassword() {
    // Pre-populate email in forgot password form
    goto(`/forgotpassword?email=${encodeURIComponent(email)}`);
  }
</script>

<div class="bg-blue-50 border border-blue-200 rounded-md p-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <h3 class="text-sm font-medium text-blue-800">
        Account already exists
      </h3>
      <div class="mt-2 text-sm text-blue-700">
        <p>
          An account with <strong>{email}</strong> already exists and is active. 
          You can log in to access your account, or reset your password if you've forgotten it.
        </p>
      </div>
      
      <div class="mt-4 space-x-3">
        <button
          onclick={handleGoToLogin}
          class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
          </svg>
          Go to Login
        </button>
        
        <button
          onclick={handleResetPassword}
          class="inline-flex items-center px-3 py-2 border border-blue-300 text-sm leading-4 font-medium rounded-md text-blue-700 bg-white hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          <svg class="mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m0 0a2 2 0 012 2v6a2 2 0 01-2 2h-6a2 2 0 01-2-2V9a2 2 0 012-2m0 0V7a2 2 0 012-2m-2 2a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 00-2 2v6a2 2 0 002 2h6a2 2 0 002-2V9a2 2 0 00-2-2H9z" />
          </svg>
          Reset Password
        </button>
        
        {#if onBackToForm}
          <button
            onclick={onBackToForm}
            class="text-sm font-medium text-blue-600 hover:text-blue-500"
          >
            Back to Registration
          </button>
        {/if}
      </div>
    </div>
  </div>
</div>

<!-- Helpful Tips -->
<div class="bg-gray-50 border border-gray-200 rounded-md p-4 mt-4">
  <div class="flex">
    <div class="flex-shrink-0">
      <svg class="h-5 w-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
      </svg>
    </div>
    <div class="ml-3">
      <h3 class="text-sm font-medium text-gray-800">
        Need help accessing your account?
      </h3>
      <div class="mt-2 text-sm text-gray-600">
        <ul class="list-disc list-inside space-y-1">
          <li>If you remember your password, use "Go to Login"</li>
          <li>If you forgot your password, use "Reset Password"</li>
          <li>Check if you might have used a different email address</li>
          <li>Contact support if you're still having trouble</li>
        </ul>
      </div>
      <div class="mt-3">
        <p class="text-xs text-gray-500">
          Need help? 
          <a href="mailto:<EMAIL>" class="font-medium text-blue-600 hover:text-blue-500">
            Contact Support
          </a>
        </p>
      </div>
    </div>
  </div>
</div>
