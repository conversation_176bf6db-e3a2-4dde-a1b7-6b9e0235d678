@import "tailwindcss";

@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --radius: 0.625rem;
  --background: oklch(1.00000 0.00000 0);
  --foreground: oklch(0.14100 0.00500 285.82300);
  --card: oklch(1.00000 0.00000 0);
  --card-foreground: oklch(0.14100 0.00500 285.82300);
  --popover: oklch(1.00000 0.00000 0);
  --popover-foreground: oklch(0.14100 0.00500 285.82300);
  --primary: oklch(0.54596 0.21532 262.87191);
  --primary-foreground: oklch(0.14100 0.00500 285.82300);
  --secondary: oklch(0.96844 0.00682 247.89514);
  --secondary-foreground: oklch(0.14100 0.00500 285.82300);
  --accent: oklch(0.96844 0.00682 247.89514);
  --accent-foreground: oklch(0.14100 0.00500 285.82300);
  --destructive: oklch(0.63683 0.20776 25.32589);
  --destructive-foreground: oklch(0.98500 0.00000 0.00000);
  --muted: oklch(0.96955 0.00000 0);
  --muted-foreground: oklch(0.14100 0.00500 285.82300);
  --border: oklch(0.91953 0.00630 266.25971);
  --input: oklch(0.91953 0.00630 266.25971);
  --ring: oklch(0.54596 0.21532 262.87191);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(1.00000 0.00000 0);
  --sidebar-foreground: oklch(0.14100 0.00500 285.82300);
  --sidebar-primary: oklch(0.54596 0.21532 262.87191);
  --sidebar-primary-foreground: oklch(0.14100 0.00500 285.82300);
  --sidebar-accent: oklch(0.96844 0.00682 247.89514);
  --sidebar-accent-foreground: oklch(0.14100 0.00500 285.82300);
  --sidebar-border: oklch(0.91953 0.00630 266.25971);
  --sidebar-ring: oklch(0.54596 0.21532 262.87191);
}

.dark {
  --background: oklch(0.13706 0.03597 258.52581);
  --foreground: oklch(0.98500 0.00000 0.00000);
  --card: oklch(0.13706 0.03597 258.52581);
  --card-foreground: oklch(0.98500 0.00000 0.00000);
  --popover: oklch(0.19946 0.00836 266.04093);
  --popover-foreground: oklch(0.98500 0.00000 0.00000);
  --primary: oklch(0.62318 0.18793 259.79641);
  --primary-foreground: oklch(0.14100 0.00500 285.82300);
  --secondary: oklch(0.28001 0.03693 259.97396);
  --secondary-foreground: oklch(0.98500 0.00000 0.00000);
  --accent: oklch(0.28001 0.03693 259.97396);
  --accent-foreground: oklch(0.98500 0.00000 0.00000);
  --destructive: oklch(0.63683 0.20776 25.32589);
  --destructive-foreground: oklch(0.98500 0.00000 0.00000);
  --muted: oklch(0.23278 0.08866 263.36905);
  --muted-foreground: oklch(0.98500 0.00000 0.00000);
  --border: oklch(0.27105 0.01373 265.97524);
  --input: oklch(0.27105 0.01373 265.97524);
  --ring: oklch(0.62318 0.18793 259.79641);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.13706 0.03597 258.52581);
  --sidebar-foreground: oklch(0.98500 0.00000 0.00000);
  --sidebar-primary: oklch(0.62318 0.18793 259.79641);
  --sidebar-primary-foreground: oklch(0.14100 0.00500 285.82300);
  --sidebar-accent: oklch(0.28001 0.03693 259.97396);
  --sidebar-accent-foreground: oklch(0.98500 0.00000 0.00000);
  --sidebar-border: oklch(0.27105 0.01373 265.97524);
  --sidebar-ring: oklch(0.62318 0.18793 259.79641);
}

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Performance-optimized sidebar animations with GPU acceleration */
@layer components {
  /* Sidebar root container with hardware acceleration */
  [data-slot="sidebar-root"] {
    transition: transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    transform: translateZ(0); /* Force GPU layer */
  }

  /* Smooth transitions for sidebar content without layout thrashing */
  [data-slot="sidebar-content"],
  [data-slot="sidebar-header"],
  [data-slot="sidebar-footer"] {
    transition: 
      opacity 300ms cubic-bezier(0.4, 0, 0.2, 1),
      transform 300ms cubic-bezier(0.4, 0, 0.2, 1);
    will-change: opacity, transform;
  }

  /* Enhanced sidebar item transitions */
  [data-sidebar="menu-button"] {
    transition: 
      background-color 200ms cubic-bezier(0.4, 0, 0.2, 1),
      transform 200ms cubic-bezier(0.4, 0, 0.2, 1),
      box-shadow 200ms cubic-bezier(0.4, 0, 0.2, 1);
    will-change: background-color, transform, box-shadow;
  }

  /* Active state with 3px accent border following best practices */
  [data-sidebar="menu-button"][data-active="true"] {
    background-color: rgb(219 234 254 / 0.8) !important;
    color: rgb(29 78 216) !important;
    border-left: 3px solid rgb(29 78 216);
    font-weight: 500;
    box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1);
    transform: translateX(2px);
  }

  /* Enhanced hover states */
  [data-sidebar="menu-button"]:hover:not([data-active="true"]) {
    background-color: rgb(0 0 0 / 0.03);
    transform: translateX(1px) scale(1.01);
  }

  /* Toggle button enhancements */
  .sidebar-toggle {
    transition: 
      background-color 200ms cubic-bezier(0.4, 0, 0.2, 1),
      transform 200ms cubic-bezier(0.4, 0, 0.2, 1),
      border-color 200ms cubic-bezier(0.4, 0, 0.2, 1);
    will-change: background-color, transform, border-color;
  }

  .sidebar-toggle:hover {
    background-color: rgb(0 0 0 / 0.05);
    transform: scale(1.05);
    border-color: rgb(59 130 246 / 0.3);
  }

  /* Accessibility: Respect user motion preferences */
  @media (prefers-reduced-motion: reduce) {
    [data-slot="sidebar-root"],
    [data-slot="sidebar-content"],
    [data-slot="sidebar-header"],
    [data-slot="sidebar-footer"],
    [data-sidebar="menu-button"],
    .sidebar-toggle {
      transition: none !important;
      will-change: auto !important;
      transform: none !important;
    }
  }

  /* Dark mode active states */
  .dark [data-sidebar="menu-button"][data-active="true"] {
    background-color: rgb(30 58 138 / 0.8) !important;
    color: rgb(147 197 253) !important;
    border-left: 3px solid rgb(147 197 253);
  }

  /* Dark mode hover states */
  .dark [data-sidebar="menu-button"]:hover:not([data-active="true"]) {
    background-color: rgb(255 255 255 / 0.05);
  }

  .dark .sidebar-toggle:hover {
    background-color: rgb(255 255 255 / 0.08);
    border-color: rgb(59 130 246 / 0.4);
  }
}
