#!/bin/bash
# Cleanup script for backup files

echo "Removing auth backup directories..."
rm -rf "src/routes/(auth)/login.backup"
rm -rf "src/routes/(auth)/register.backup" 
rm -rf "src/routes/(auth)/verifyhuman.backup"

echo "Removing component backup files..."
rm -f "src/lib/components/AuthDebug.svelte.backup"
rm -f "src/lib/components/AuthDebugPanel.svelte.backup"
rm -f "src/lib/components/Turnstile.svelte.backup"
rm -f "src/lib/components/TurnstilePreClearance.svelte.backup"
rm -f "src/lib/components/TurnstileWrapper.svelte.backup"
rm -f "src/lib/components/ui/TurnstileVerification.svelte.backup"

echo "Cleanup completed!"
