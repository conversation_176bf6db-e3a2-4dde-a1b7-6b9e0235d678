# SourceFlex Robots.txt - Job Board Protection
# Protect job listings from AI scraping while allowing legitimate search engines

# Allow main search engines for general pages
User-agent: Googlebot
Allow: /
Allow: /about
Allow: /pricing
Allow: /contact
Allow: /blog
Disallow: /dashboard
Disallow: /jobs/*
Disallow: /api/*

User-agent: Bingbot
Allow: /
Allow: /about
Allow: /pricing
Allow: /contact
Allow: /blog
Disallow: /dashboard
Disallow: /jobs/*
Disallow: /api/*

# Block all AI training crawlers
User-agent: GPTBot
Disallow: /

User-agent: ChatGPT-User
Disallow: /

User-agent: anthropic-ai
Disallow: /

User-agent: Google-Extended
Disallow: /

User-agent: Bytespider
Disallow: /

User-agent: ClaudeBot
Disallow: /

User-agent: Meta-ExternalAgent
Disallow: /

User-agent: FacebookBot
Disallow: /

User-agent: Amazonbot
Disallow: /

User-agent: PerplexityBot
Disallow: /

User-agent: YouBot
Disallow: /

# Block aggressive crawlers and bad bots
User-agent: Nuclei
Disallow: /

User-agent: WikiDo
Disallow: /

User-agent: Riddler
Disallow: /

User-agent: PetalBot
Disallow: /

User-agent: Zoominfobot
Disallow: /

User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: BLEXBot
Disallow: /

User-agent: SemrushBot
Disallow: /

User-agent: DotBot
Disallow: /

# Default rule for unlisted bots
User-agent: *
Allow: /
Allow: /about
Allow: /pricing
Allow: /contact
Allow: /blog
Disallow: /dashboard
Disallow: /jobs/*
Disallow: /api/*
Disallow: /admin

# Crawl delay for general bots (prevent overload)
Crawl-delay: 10

# Sitemap location (add once you have one)
# Sitemap: https://sourceflex.com/sitemap.xml
