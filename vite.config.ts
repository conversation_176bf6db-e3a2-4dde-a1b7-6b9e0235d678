import tailwindcss from '@tailwindcss/vite';
import { sveltekit } from '@sveltejs/kit/vite';
import houdini from 'houdini/vite';
import { defineConfig, type PluginOption, loadEnv } from 'vite';

// Only run port cleanup in development environment
if (process.env.NODE_ENV !== 'production') {
	try {
		const { execSync } = await import('child_process');
		execSync('lsof -ti:5173 | xargs kill -9', { stdio: 'ignore' });
		console.log('🧹 Cleaned up port 5173');
	} catch {
		// Port was already free, no action needed
	}
}

export default defineConfig(({ mode }) => {
	// Load environment variables
	const env = loadEnv(mode, process.cwd(), '');
	
	const plugins: PluginOption[] = [
		houdini(), // Must come before sveltekit
		tailwindcss(),
		sveltekit()
	];

	return {
		plugins,
		server: {
			port: 5173,
			strictPort: true
		},
		define: {
			// Make environment variables available to the client
			'import.meta.env.PUBLIC_TURNSTILE_SITE_KEY': JSON.stringify(env.PUBLIC_TURNSTILE_SITE_KEY),
			'import.meta.env.PUBLIC_APP_NAME': JSON.stringify(env.PUBLIC_APP_NAME),
			'import.meta.env.PUBLIC_NHOST_SUBDOMAIN': JSON.stringify(env.PUBLIC_NHOST_SUBDOMAIN),
			'import.meta.env.PUBLIC_NHOST_REGION': JSON.stringify(env.PUBLIC_NHOST_REGION),
			'import.meta.env.PUBLIC_GRAPHQL_ENDPOINT': JSON.stringify(env.PUBLIC_GRAPHQL_ENDPOINT),
		}
	};
});
